from django.contrib.auth.tokens import default_token_generator
from django.core.mail import send_mail
from django.conf import settings
from django.utils.http import urlsafe_base64_encode, urlsafe_base64_decode
from django.contrib.auth.models import User
from django.utils.encoding import force_bytes, force_str
from django.core.exceptions import ValidationError
from django.contrib.auth.password_validation import validate_password
import textwrap

from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.views import APIView, Response, status
from rest_framework import generics, serializers
from rest_framework_simplejwt.tokens import RefreshToken
from rest_framework_simplejwt.views import TokenObtainPairView
from rest_framework_simplejwt.serializers import TokenObtainPairSerializer

from .serializers import RegisterSerializer


class RegisterAPIView(generics.CreateAPIView):
    queryset = User.objects.all()
    # permission_classes = (AllowAny,)
    serializer_class = RegisterSerializer


class PasswordResetView(APIView):
    permission_classes = [AllowAny]
    
    def post(self, request):
        email = request.data.get('email')
        
        if not email:
            return Response(
                {"error": "Email é obrigatório"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            user = User.objects.get(email=email)
            # Gerar token e UID
            uid = urlsafe_base64_encode(force_bytes(user.pk))
            token = default_token_generator.make_token(user)
            
            # Building reset URL (frontend usará isso)
            if settings.DEBUG:
                reset_url = f"http://localhost:5173/reset-password/{uid}/{token}/"  # locally
            else:
                reset_url = f"{settings.PRODUCTION_URL}/reset-password/{uid}/{token}/"  # production
            
            # Enviar email
            subject = "Recuperação de senha"
            message = textwrap.dedent(f"""
            Olá,

            Recebemos uma solicitação para redefinir a senha da sua conta.

            Para redefinir sua senha, clique no link abaixo:

            {reset_url}

            Este link é válido por {int(settings.PASSWORD_RESET_TIMEOUT / (60*60))} horas.

            Se você não solicitou a redefinição de senha, por favor ignore este e-mail.

            Atenciosamente,
            Equipe de Desenvolvimento
            LESC
            """)

            send_mail(
                subject,
                message,
                settings.DEFAULT_FROM_EMAIL,
                [user.email],
                fail_silently=False,
            )

            # email = EmailMultiAlternatives(
            #     subject=subject,
            #     body=message,
            #     from_email=settings.DEFAULT_FROM_EMAIL,
            #     to=[user.email],
            # )
            # email.attach_alternative(html_content, "text/html")
            # email.send()
            
            return Response(
                {"success": "Email de recuperação enviado com sucesso."},
                status=status.HTTP_200_OK
            )
            
        except User.DoesNotExist:
            # Não revele que o usuário não existe por questões de segurança
            return Response(
                {"success": "Se o email estiver cadastrado, um link de recuperação será enviado."},
                status=status.HTTP_200_OK
            )


class PasswordResetConfirmView(APIView):
    permission_classes = [AllowAny]
    
    def post(self, request):
        uid = request.data.get('uid')
        token = request.data.get('token')
        password = request.data.get('password')
        
        if not (uid and token and password):
            return Response(
                {"error": "UID, token e nova senha são obrigatórios"},
                status=status.HTTP_400_BAD_REQUEST
            )
  
        try:
            # Decodificar o UID para obter o ID do usuário
            user_id = force_str(urlsafe_base64_decode(uid))
            user = User.objects.get(pk=user_id)
            
            # Verificar se o token é válido
            if default_token_generator.check_token(user, token):
                # Validar senha
                try:
                    validate_password(password, user)
                except ValidationError as ve:
                    return Response({'error': ve}, status=status.HTTP_400_BAD_REQUEST)

                # Definir nova senha
                user.set_password(password)
                user.save()
                
                # Gerar novos tokens JWT
                refresh = RefreshToken.for_user(user)
                
                return Response({
                    "success": "Senha alterada com sucesso.",
                    "refresh": str(refresh),
                    "access": str(refresh.access_token),
                }, status=status.HTTP_200_OK)
            else:
                return Response(
                    {"error": "O link de recuperação é inválido ou expirou."},
                    status=status.HTTP_400_BAD_REQUEST
                )
                
        except (TypeError, ValueError, OverflowError, User.DoesNotExist):
            return Response(
                {"error": "Link de recuperação inválido."},
                status=status.HTTP_400_BAD_REQUEST
            )


class PasswordChangeView(APIView):
    """View para mudar a senha quando o usuário está logado"""
    permission_classes = [IsAuthenticated]
    
    def post(self, request):
        old_password = request.data.get('old_password')
        new_password = request.data.get('new_password')
        
        if not (old_password and new_password):
            return Response(
                {"error": "Senha atual e nova senha são obrigatórias"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        user = request.user
        
        # Verificar senha atual
        if not user.check_password(old_password):
            return Response(
                {"error": "Senha atual incorreta"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Validar senha
        try:
            validate_password(new_password, user)
        except ValidationError as ve:
            return Response({'error': ve}, status=status.HTTP_400_BAD_REQUEST)
        # Definir nova senha
        user.set_password(new_password)
        user.save()
        
        # Gerar novos tokens JWT
        refresh = RefreshToken.for_user(user)
        
        return Response({
            "success": "Senha alterada com sucesso",
            "refresh": str(refresh),
            "access": str(refresh.access_token),
        }, status=status.HTTP_200_OK)


class CustomTokenObtainPairSerializer(TokenObtainPairSerializer):
    """Custom serializer to provide better error messages for authentication"""

    def validate(self, attrs):
        try:
            data = super().validate(attrs)
            return data
        except Exception as e:
            # Provide more user-friendly error messages
            raise serializers.ValidationError({
                'detail': 'Usuário ou senha incorretos. Verifique suas credenciais e tente novamente.'
            })


class CustomTokenObtainPairView(TokenObtainPairView):
    """Custom token view with enhanced error handling"""
    serializer_class = CustomTokenObtainPairSerializer

    def post(self, request, *args, **kwargs):
        try:
            return super().post(request, *args, **kwargs)
        except Exception as e:
            return Response({
                'error': 'Erro ao processar login. Tente novamente.'
            }, status=status.HTTP_400_BAD_REQUEST)
