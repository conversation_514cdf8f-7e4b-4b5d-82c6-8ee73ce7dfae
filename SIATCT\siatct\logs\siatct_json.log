{"time": "2025-05-16 14:10:21,892", "level": "INFO", "logger": "django.utils.autoreload", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\django\utils\autoreload.py", "line": 667, "message": "Watching for file changes with StatReloader"}
{"time": "2025-05-16 14:10:52,420", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP OPTIONS /api/v1/exams/ 200 [0.00, 127.0.0.1:58199]"}
{"time": "2025-05-16 14:10:52,422", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP OPTIONS /api/v1/exams/ 200 [0.00, 127.0.0.1:58200]"}
{"time": "2025-05-16 14:10:52,714", "level": "WARNING", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 178, "message": "HTTP GET /api/v1/exams/ 401 [0.29, 127.0.0.1:58199]"}
{"time": "2025-05-16 14:10:52,721", "level": "WARNING", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 178, "message": "HTTP GET /api/v1/exams/ 401 [0.01, 127.0.0.1:58199]"}
{"time": "2025-05-16 14:11:00,951", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP OPTIONS /api/v1/authentication/token/ 200 [0.00, 127.0.0.1:58199]"}
{"time": "2025-05-16 14:11:02,042", "level": "WARNING", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 178, "message": "HTTP POST /api/v1/authentication/token/ 401 [1.09, 127.0.0.1:58199]"}
{"time": "2025-05-16 14:11:06,361", "level": "WARNING", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 178, "message": "HTTP POST /api/v1/authentication/token/ 401 [0.95, 127.0.0.1:58199]"}
{"time": "2025-05-16 14:11:38,871", "level": "INFO", "logger": "django.utils.autoreload", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\django\utils\autoreload.py", "line": 667, "message": "Watching for file changes with StatReloader"}
{"time": "2025-05-16 14:12:03,485", "level": "WARNING", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 178, "message": "HTTP POST /api/v1/authentication/token/ 401 [1.07, 127.0.0.1:58236]"}
{"time": "2025-05-16 14:12:51,026", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP OPTIONS /api/v1/authentication/register/ 200 [0.00, 127.0.0.1:58236]"}
{"time": "2025-05-16 14:12:51,200", "level": "WARNING", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 178, "message": "HTTP POST /api/v1/authentication/register/ 400 [0.02, 127.0.0.1:58243]"}
{"time": "2025-05-16 14:12:52,214", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP POST /api/v1/authentication/register/ 201 [1.19, 127.0.0.1:58236]"}
{"time": "2025-05-16 14:13:01,197", "level": "WARNING", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 178, "message": "HTTP POST /api/v1/authentication/token/ 401 [1.06, 127.0.0.1:58236]"}
{"time": "2025-05-16 14:13:05,305", "level": "WARNING", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 178, "message": "HTTP POST /api/v1/authentication/token/ 401 [1.09, 127.0.0.1:58236]"}
{"time": "2025-05-16 14:13:38,995", "level": "WARNING", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 178, "message": "HTTP POST /api/v1/authentication/token/ 401 [1.06, 127.0.0.1:58236]"}
{"time": "2025-05-16 14:13:47,212", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP POST /api/v1/authentication/token/ 200 [1.03, 127.0.0.1:58236]"}
{"time": "2025-05-16 14:13:47,243", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/exams/ 200 [0.01, 127.0.0.1:58236]"}
{"time": "2025-05-16 14:13:47,261", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/exams/ 200 [0.01, 127.0.0.1:58236]"}
{"time": "2025-05-16 14:14:14,744", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP POST /api/v1/exams/ 201 [1.00, 127.0.0.1:58236]"}
{"time": "2025-05-16 14:14:14,771", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/exams/ 200 [0.02, 127.0.0.1:58236]"}
{"time": "2025-05-16 14:14:16,330", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/1/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.911257607240619696148712587012.dcm 200 [0.06, 127.0.0.1:58236]"}
{"time": "2025-05-16 14:15:24,479", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/exams/ 200 [0.02, 127.0.0.1:58279]"}
{"time": "2025-05-16 14:15:26,103", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 172, "message": "HTTP GET /media/exams/1/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.911257607240619696148712587012.dcm 304 [0.00, 127.0.0.1:58279]"}
{"time": "2025-05-16 14:15:29,381", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP OPTIONS /api/v1/series/segmentation/new/ 200 [0.00, 127.0.0.1:58279]"}
{"time": "2025-05-16 14:15:30,619", "level": "ERROR", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 181, "message": "HTTP POST /api/v1/series/segmentation/new/ 500 [1.24, 127.0.0.1:58279]"}
{"time": "2025-05-16 14:15:32,226", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/1/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.184801338381895107635381026857.dcm 200 [0.00, 127.0.0.1:58279]"}
{"time": "2025-05-16 14:15:32,236", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/1/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.227252232966919521559290115017.dcm 200 [0.01, 127.0.0.1:58281]"}
{"time": "2025-05-16 14:15:32,261", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/1/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.305763411933569638615761005863.dcm 200 [0.01, 127.0.0.1:58281]"}
{"time": "2025-05-16 14:15:32,278", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/1/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.892649213333065045357131733969.dcm 200 [0.01, 127.0.0.1:58281]"}
{"time": "2025-05-16 14:15:32,303", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/1/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.210616673074470338920284866510.dcm 200 [0.01, 127.0.0.1:58281]"}
{"time": "2025-05-16 14:15:32,569", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/1/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.308998466994580007520425874686.dcm 200 [0.01, 127.0.0.1:58281]"}
{"time": "2025-05-16 14:15:32,576", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/1/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.254916896710732470846876971852.dcm 200 [0.01, 127.0.0.1:58279]"}
{"time": "2025-05-16 14:15:32,594", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/1/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.255571364135736589219701492950.dcm 200 [0.00, 127.0.0.1:58279]"}
{"time": "2025-05-16 14:15:32,628", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/1/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.147995027728672345689485008579.dcm 200 [0.01, 127.0.0.1:58279]"}
{"time": "2025-05-16 14:15:33,128", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/1/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.204716343021109697619551710230.dcm 200 [0.00, 127.0.0.1:58279]"}
{"time": "2025-05-16 14:15:33,143", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/1/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.283914737330506508971730533367.dcm 200 [0.01, 127.0.0.1:58279]"}
{"time": "2025-05-16 14:15:33,179", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/1/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.184131899543495374569818432657.dcm 200 [0.01, 127.0.0.1:58279]"}
{"time": "2025-05-16 14:15:33,188", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/1/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.264364194296293440320787350583.dcm 200 [0.01, 127.0.0.1:58281]"}
{"time": "2025-05-16 14:15:33,236", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/1/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.216515627058008697217695696166.dcm 200 [0.01, 127.0.0.1:58281]"}
{"time": "2025-05-16 14:31:37,997", "level": "INFO", "logger": "django.utils.autoreload", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\django\utils\autoreload.py", "line": 667, "message": "Watching for file changes with StatReloader"}
{"time": "2025-05-16 14:33:48,630", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP OPTIONS /api/v1/authentication/request-password-reset/ 200 [0.00, 127.0.0.1:58783]"}
{"time": "2025-05-16 14:36:02,680", "level": "WARNING", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 178, "message": "HTTP POST /api/v1/authentication/register/ 400 [0.06, 127.0.0.1:58942]"}
{"time": "2025-05-16 14:42:19,173", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP OPTIONS /api/v1/password-reset/ 200 [0.00, 127.0.0.1:59264]"}
{"time": "2025-05-16 14:44:55,018", "level": "ERROR", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 181, "message": "HTTP POST /api/v1/password-reset/ 500 [0.26, 127.0.0.1:59471]"}
{"time": "2025-05-16 15:11:15,738", "level": "INFO", "logger": "django.utils.autoreload", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\django\utils\autoreload.py", "line": 667, "message": "Watching for file changes with StatReloader"}
{"time": "2025-05-16 15:11:26,967", "level": "ERROR", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 181, "message": "HTTP POST /api/v1/password-reset/ 500 [0.07, 127.0.0.1:60399]"}
{"time": "2025-05-16 15:12:48,091", "level": "INFO", "logger": "django.utils.autoreload", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\django\utils\autoreload.py", "line": 667, "message": "Watching for file changes with StatReloader"}
{"time": "2025-05-16 15:13:04,117", "level": "INFO", "logger": "django.utils.autoreload", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\django\utils\autoreload.py", "line": 667, "message": "Watching for file changes with StatReloader"}
{"time": "2025-05-16 15:13:11,899", "level": "ERROR", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 181, "message": "HTTP POST /api/v1/password-reset/ 500 [0.07, 127.0.0.1:60484]"}
{"time": "2025-05-16 15:13:13,201", "level": "ERROR", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 181, "message": "HTTP POST /api/v1/password-reset/ 500 [0.07, 127.0.0.1:60486]"}
{"time": "2025-05-16 15:13:15,397", "level": "ERROR", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 181, "message": "HTTP POST /api/v1/password-reset/ 500 [0.08, 127.0.0.1:60489]"}
{"time": "2025-05-16 15:16:09,434", "level": "INFO", "logger": "django.utils.autoreload", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\django\utils\autoreload.py", "line": 667, "message": "Watching for file changes with StatReloader"}
{"time": "2025-05-16 15:16:22,171", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP POST /api/v1/password-reset/ 200 [1.84, 127.0.0.1:60593]"}
{"time": "2025-05-16 15:18:50,636", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP POST /api/v1/password-reset/ 200 [1.05, 127.0.0.1:60813]"}
{"time": "2025-05-16 15:19:12,809", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP POST /api/v1/password-reset/confirm/ 200 [1.10, 127.0.0.1:60833]"}
{"time": "2025-05-16 15:19:22,164", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP POST /api/v1/authentication/token/ 200 [1.08, 127.0.0.1:60845]"}
{"time": "2025-05-16 15:19:22,220", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/exams/ 200 [0.03, 127.0.0.1:60848]"}
{"time": "2025-05-16 15:19:22,245", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/exams/ 200 [0.02, 127.0.0.1:60850]"}
{"time": "2025-05-16 15:27:49,346", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/exams/ 200 [0.02, 127.0.0.1:61128]"}
{"time": "2025-05-16 15:27:49,369", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/exams/ 200 [0.02, 127.0.0.1:61130]"}
{"time": "2025-05-16 15:34:24,104", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP POST /api/v1/password-reset/ 200 [1.23, 127.0.0.1:61333]"}
{"time": "2025-05-16 15:35:17,273", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP POST /api/v1/password-reset/confirm/ 200 [1.02, 127.0.0.1:61373]"}
{"time": "2025-05-16 15:35:30,500", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP POST /api/v1/authentication/token/ 200 [1.05, 127.0.0.1:61386]"}
{"time": "2025-05-16 15:35:30,553", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/exams/ 200 [0.02, 127.0.0.1:61390]"}
{"time": "2025-05-16 15:35:30,575", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/exams/ 200 [0.02, 127.0.0.1:61392]"}
{"time": "2025-05-16 15:35:31,455", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP POST /api/v1/authentication/token/ 200 [1.02, 127.0.0.1:61388]"}
{"time": "2025-05-16 15:35:31,482", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/exams/ 200 [0.01, 127.0.0.1:61395]"}
{"time": "2025-05-16 15:47:13,564", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/exams/ 200 [0.02, 127.0.0.1:61958]"}
{"time": "2025-05-16 15:47:13,591", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/exams/ 200 [0.02, 127.0.0.1:61959]"}
{"time": "2025-05-16 15:48:00,882", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/exams/ 200 [0.02, 127.0.0.1:62050]"}
{"time": "2025-05-16 15:48:24,738", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP POST /api/v1/authentication/token/ 200 [1.00, 127.0.0.1:62096]"}
{"time": "2025-05-16 15:48:24,795", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/exams/ 200 [0.03, 127.0.0.1:62100]"}
{"time": "2025-05-16 15:48:24,818", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/exams/ 200 [0.02, 127.0.0.1:62102]"}
{"time": "2025-05-16 15:48:41,776", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP DELETE /api/v1/patients/delete/1/ 204 [0.09, 127.0.0.1:62156]"}
{"time": "2025-05-16 15:48:41,795", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/exams/ 200 [0.01, 127.0.0.1:62159]"}
{"time": "2025-05-16 15:48:57,217", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP POST /api/v1/exams/ 201 [0.76, 127.0.0.1:62201]"}
{"time": "2025-05-16 15:48:57,242", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/exams/ 200 [0.02, 127.0.0.1:62204]"}
{"time": "2025-05-16 15:49:54,304", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/exams/ 200 [0.02, 127.0.0.1:62312]"}
{"time": "2025-05-16 15:50:05,732", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/exams/ 200 [0.02, 127.0.0.1:62333]"}
{"time": "2025-05-16 15:50:05,761", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/exams/ 200 [0.02, 127.0.0.1:62334]"}
{"time": "2025-05-16 15:50:05,780", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/exams/ 200 [0.02, 127.0.0.1:62335]"}
{"time": "2025-05-16 15:50:05,799", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/exams/ 200 [0.02, 127.0.0.1:62336]"}
{"time": "2025-05-16 15:53:42,021", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/exams/ 200 [0.02, 127.0.0.1:62548]"}
{"time": "2025-05-16 15:53:47,851", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/exams/ 200 [0.03, 127.0.0.1:62551]"}
{"time": "2025-05-16 15:53:47,873", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/exams/ 200 [0.02, 127.0.0.1:62553]"}
{"time": "2025-05-16 15:54:28,930", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/exams/ 200 [0.02, 127.0.0.1:62588]"}
{"time": "2025-05-16 15:54:31,603", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/exams/ 200 [0.02, 127.0.0.1:62590]"}
{"time": "2025-05-16 15:54:31,624", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/exams/ 200 [0.02, 127.0.0.1:62591]"}
{"time": "2025-05-16 15:54:32,671", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/exams/ 200 [0.02, 127.0.0.1:62596]"}
{"time": "2025-05-16 15:54:33,618", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/exams/ 200 [0.02, 127.0.0.1:62599]"}
{"time": "2025-05-16 15:55:22,594", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP POST /api/v1/authentication/token/ 200 [0.91, 127.0.0.1:62707]"}
{"time": "2025-05-16 15:55:22,648", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/exams/ 200 [0.03, 127.0.0.1:62709]"}
{"time": "2025-05-16 15:55:22,670", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/exams/ 200 [0.02, 127.0.0.1:62711]"}
{"time": "2025-05-16 15:56:40,037", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/exams/ 200 [0.02, 127.0.0.1:62910]"}
{"time": "2025-05-16 15:58:58,062", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/exams/ 200 [0.02, 127.0.0.1:63037]"}
{"time": "2025-05-16 15:58:58,084", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/exams/ 200 [0.02, 127.0.0.1:63038]"}
{"time": "2025-05-16 16:03:29,213", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/exams/ 200 [0.02, 127.0.0.1:63207]"}
{"time": "2025-05-16 16:03:34,311", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/exams/ 200 [0.02, 127.0.0.1:63207]"}
{"time": "2025-05-16 16:03:35,785", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.911257607240619696148712587012.dcm 200 [0.05, 127.0.0.1:63207]"}
{"time": "2025-05-16 16:03:36,869", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.227252232966919521559290115017.dcm 200 [0.01, 127.0.0.1:63221]"}
{"time": "2025-05-16 16:03:36,870", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.184801338381895107635381026857.dcm 200 [0.02, 127.0.0.1:63207]"}
{"time": "2025-05-16 16:03:36,892", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.305763411933569638615761005863.dcm 200 [0.01, 127.0.0.1:63207]"}
{"time": "2025-05-16 16:03:36,921", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.892649213333065045357131733969.dcm 200 [0.01, 127.0.0.1:63207]"}
{"time": "2025-05-16 16:03:36,948", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.210616673074470338920284866510.dcm 200 [0.01, 127.0.0.1:63207]"}
{"time": "2025-05-16 16:03:36,978", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.308998466994580007520425874686.dcm 200 [0.02, 127.0.0.1:63207]"}
{"time": "2025-05-16 16:03:37,277", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.254916896710732470846876971852.dcm 200 [0.01, 127.0.0.1:63207]"}
{"time": "2025-05-16 16:03:37,312", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.255571364135736589219701492950.dcm 200 [0.02, 127.0.0.1:63207]"}
{"time": "2025-05-16 16:03:37,338", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.147995027728672345689485008579.dcm 200 [0.03, 127.0.0.1:63221]"}
{"time": "2025-05-16 16:03:37,371", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.283914737330506508971730533367.dcm 200 [0.02, 127.0.0.1:63221]"}
{"time": "2025-05-16 16:03:37,374", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.204716343021109697619551710230.dcm 200 [0.04, 127.0.0.1:63207]"}
{"time": "2025-05-16 16:03:37,388", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.184131899543495374569818432657.dcm 200 [0.01, 127.0.0.1:63207]"}
{"time": "2025-05-16 16:03:37,400", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.264364194296293440320787350583.dcm 200 [0.01, 127.0.0.1:63221]"}
{"time": "2025-05-16 16:03:45,124", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/exams/ 200 [0.03, 127.0.0.1:63233]"}
{"time": "2025-05-16 16:03:48,587", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/exams/ 200 [0.02, 127.0.0.1:63241]"}
{"time": "2025-05-16 16:03:54,178", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/exams/ 200 [0.04, 127.0.0.1:63221]"}
{"time": "2025-05-16 16:03:56,452", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/exams/ 200 [0.02, 127.0.0.1:63221]"}
{"time": "2025-05-16 16:03:59,455", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.216515627058008697217695696166.dcm 200 [0.02, 127.0.0.1:63221]"}
{"time": "2025-05-16 16:03:59,469", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.223280971440388131399634898721.dcm 200 [0.01, 127.0.0.1:63207]"}
{"time": "2025-05-16 16:04:04,140", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/exams/ 200 [0.02, 127.0.0.1:63207]"}
{"time": "2025-05-16 16:10:47,052", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP OPTIONS /api/v1/password-reset/ 200 [0.00, 127.0.0.1:63311]"}
{"time": "2025-05-16 16:10:48,083", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP POST /api/v1/password-reset/ 200 [1.03, 127.0.0.1:63311]"}
{"time": "2025-05-16 16:11:09,386", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP OPTIONS /api/v1/password-reset/confirm/ 200 [0.00, 127.0.0.1:63311]"}
{"time": "2025-05-16 16:11:10,389", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP POST /api/v1/password-reset/confirm/ 200 [1.00, 127.0.0.1:63311]"}
{"time": "2025-05-16 16:11:21,713", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP OPTIONS /api/v1/authentication/token/ 200 [0.00, 127.0.0.1:63311]"}
{"time": "2025-05-16 16:11:22,633", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP POST /api/v1/authentication/token/ 200 [0.92, 127.0.0.1:63311]"}
{"time": "2025-05-16 16:11:22,663", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP OPTIONS /api/v1/exams/ 200 [0.00, 127.0.0.1:63311]"}
{"time": "2025-05-16 16:11:22,684", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/exams/ 200 [0.02, 127.0.0.1:63311]"}
{"time": "2025-05-16 16:11:22,708", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/exams/ 200 [0.02, 127.0.0.1:63311]"}
{"time": "2025-05-16 16:11:24,374", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 172, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.911257607240619696148712587012.dcm 304 [0.01, 127.0.0.1:63311]"}
{"time": "2025-05-19 10:12:32,209", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP OPTIONS /api/v1/exams/ 200 [0.00, 127.0.0.1:51368]"}
{"time": "2025-05-19 10:12:32,218", "level": "WARNING", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 178, "message": "HTTP GET /api/v1/exams/ 401 [0.01, 127.0.0.1:51368]"}
{"time": "2025-05-19 10:12:35,924", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP OPTIONS /api/v1/password-reset/ 200 [0.00, 127.0.0.1:51368]"}
{"time": "2025-05-19 10:12:37,662", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP POST /api/v1/password-reset/ 200 [1.74, 127.0.0.1:51368]"}
{"time": "2025-05-19 10:12:54,759", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP OPTIONS /api/v1/password-reset/confirm/ 200 [0.00, 127.0.0.1:51368]"}
{"time": "2025-05-19 10:12:55,711", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP POST /api/v1/password-reset/confirm/ 200 [0.95, 127.0.0.1:51368]"}
{"time": "2025-05-19 10:13:05,878", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP OPTIONS /api/v1/authentication/token/ 200 [0.00, 127.0.0.1:51368]"}
{"time": "2025-05-19 10:13:06,809", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP POST /api/v1/authentication/token/ 200 [0.93, 127.0.0.1:51368]"}
{"time": "2025-05-19 10:13:06,854", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/exams/ 200 [0.02, 127.0.0.1:51368]"}
{"time": "2025-05-19 10:13:06,873", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/exams/ 200 [0.02, 127.0.0.1:51368]"}
{"time": "2025-05-19 10:13:08,290", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 172, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.911257607240619696148712587012.dcm 304 [0.01, 127.0.0.1:51368]"}
{"time": "2025-05-19 10:13:08,806", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 172, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.184801338381895107635381026857.dcm 304 [0.01, 127.0.0.1:51368]"}
{"time": "2025-05-19 10:13:08,812", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 172, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.227252232966919521559290115017.dcm 304 [0.00, 127.0.0.1:51368]"}
{"time": "2025-05-19 10:13:08,824", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 172, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.305763411933569638615761005863.dcm 304 [0.00, 127.0.0.1:51368]"}
{"time": "2025-05-19 10:13:08,851", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 172, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.892649213333065045357131733969.dcm 304 [0.00, 127.0.0.1:51368]"}
{"time": "2025-05-19 10:13:08,879", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 172, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.210616673074470338920284866510.dcm 304 [0.00, 127.0.0.1:51368]"}
{"time": "2025-05-19 10:13:09,175", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 172, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.308998466994580007520425874686.dcm 304 [0.01, 127.0.0.1:51368]"}
{"time": "2025-05-19 10:13:09,186", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 172, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.254916896710732470846876971852.dcm 304 [0.00, 127.0.0.1:51368]"}
{"time": "2025-05-19 10:13:09,206", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 172, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.255571364135736589219701492950.dcm 304 [0.01, 127.0.0.1:51368]"}
{"time": "2025-05-19 10:13:09,214", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 172, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.147995027728672345689485008579.dcm 304 [0.00, 127.0.0.1:51368]"}
{"time": "2025-05-19 10:13:09,230", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 172, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.204716343021109697619551710230.dcm 304 [0.01, 127.0.0.1:51368]"}
{"time": "2025-05-19 10:13:09,493", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 172, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.283914737330506508971730533367.dcm 304 [0.01, 127.0.0.1:51368]"}
{"time": "2025-05-19 10:13:09,499", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 172, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.184131899543495374569818432657.dcm 304 [0.01, 127.0.0.1:51423]"}
{"time": "2025-05-19 10:13:09,504", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 172, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.264364194296293440320787350583.dcm 304 [0.00, 127.0.0.1:51423]"}
{"time": "2025-05-19 10:13:09,518", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 172, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.216515627058008697217695696166.dcm 304 [0.00, 127.0.0.1:51423]"}
{"time": "2025-05-19 10:13:09,532", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 172, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.223280971440388131399634898721.dcm 304 [0.00, 127.0.0.1:51423]"}
{"time": "2025-05-19 10:13:09,825", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.241073611031000247506142606387.dcm 200 [0.02, 127.0.0.1:51368]"}
{"time": "2025-05-19 10:13:09,827", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.218387194706247505045205169130.dcm 200 [0.03, 127.0.0.1:51423]"}
{"time": "2025-05-19 10:13:09,840", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.136943255924913899762603730997.dcm 200 [0.02, 127.0.0.1:51424]"}
{"time": "2025-05-19 10:13:09,848", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.301905642463494729146015070819.dcm 200 [0.01, 127.0.0.1:51368]"}
{"time": "2025-05-19 10:18:07,390", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP OPTIONS /api/v1/series/segmentation/new/ 200 [0.00, 127.0.0.1:51548]"}
{"time": "2025-05-19 10:18:24,541", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP POST /api/v1/series/segmentation/new/ 201 [17.15, 127.0.0.1:51548]"}
{"time": "2025-05-19 10:18:24,550", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP OPTIONS /api/v1/series/segmentation/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/?algorithm=1 200 [0.00, 127.0.0.1:51548]"}
{"time": "2025-05-19 10:18:24,573", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/series/segmentation/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/?algorithm=1 200 [0.02, 127.0.0.1:51548]"}
{"time": "2025-05-19 10:18:24,588", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP OPTIONS /api/v1/series/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/slices/params/ 200 [0.00, 127.0.0.1:51548]"}
{"time": "2025-05-19 10:18:24,605", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/series/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/slices/params/ 200 [0.02, 127.0.0.1:51548]"}
{"time": "2025-05-21 10:43:41,638", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP OPTIONS /api/v1/exams/ 200 [0.01, 127.0.0.1:57933]"}
{"time": "2025-05-21 10:43:41,641", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP OPTIONS /api/v1/exams/ 200 [0.01, 127.0.0.1:57934]"}
{"time": "2025-05-21 10:43:41,663", "level": "WARNING", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 178, "message": "HTTP GET /api/v1/exams/ 401 [0.02, 127.0.0.1:57934]"}
{"time": "2025-05-21 10:43:41,673", "level": "WARNING", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 178, "message": "HTTP GET /api/v1/exams/ 401 [0.01, 127.0.0.1:57934]"}
{"time": "2025-05-21 10:48:53,213", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP OPTIONS /api/v1/authentication/token/ 200 [0.00, 127.0.0.1:58004]"}
{"time": "2025-05-21 10:48:54,200", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP POST /api/v1/authentication/token/ 200 [0.98, 127.0.0.1:58004]"}
{"time": "2025-05-21 10:48:54,269", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/exams/ 200 [0.04, 127.0.0.1:58004]"}
{"time": "2025-05-21 10:48:54,290", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/exams/ 200 [0.02, 127.0.0.1:58004]"}
{"time": "2025-05-21 10:48:56,336", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 172, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.911257607240619696148712587012.dcm 304 [0.01, 127.0.0.1:58004]"}
{"time": "2025-05-21 10:48:56,897", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 172, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.184801338381895107635381026857.dcm 304 [0.01, 127.0.0.1:58004]"}
{"time": "2025-05-21 10:48:56,904", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 172, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.227252232966919521559290115017.dcm 304 [0.01, 127.0.0.1:58005]"}
{"time": "2025-05-21 10:48:56,914", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 172, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.305763411933569638615761005863.dcm 304 [0.01, 127.0.0.1:58005]"}
{"time": "2025-05-21 10:48:56,942", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 172, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.892649213333065045357131733969.dcm 304 [0.01, 127.0.0.1:58005]"}
{"time": "2025-05-21 10:48:56,972", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 172, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.210616673074470338920284866510.dcm 304 [0.01, 127.0.0.1:58005]"}
{"time": "2025-05-21 10:48:56,981", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 172, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.308998466994580007520425874686.dcm 304 [0.01, 127.0.0.1:58004]"}
{"time": "2025-05-21 10:48:57,050", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 172, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.254916896710732470846876971852.dcm 304 [0.01, 127.0.0.1:58004]"}
{"time": "2025-05-21 10:48:57,339", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 172, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.255571364135736589219701492950.dcm 304 [0.01, 127.0.0.1:58004]"}
{"time": "2025-05-21 10:48:57,342", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 172, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.147995027728672345689485008579.dcm 304 [0.01, 127.0.0.1:58005]"}
{"time": "2025-05-21 10:48:57,349", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 172, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.204716343021109697619551710230.dcm 304 [0.00, 127.0.0.1:58005]"}
{"time": "2025-05-21 10:48:57,364", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 172, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.283914737330506508971730533367.dcm 304 [0.01, 127.0.0.1:58005]"}
{"time": "2025-05-21 10:48:57,376", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 172, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.184131899543495374569818432657.dcm 304 [0.00, 127.0.0.1:58005]"}
{"time": "2025-05-21 10:48:57,393", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 172, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.264364194296293440320787350583.dcm 304 [0.01, 127.0.0.1:58005]"}
{"time": "2025-05-21 10:48:57,752", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 172, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.216515627058008697217695696166.dcm 304 [0.01, 127.0.0.1:58005]"}
{"time": "2025-05-21 10:48:57,763", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 172, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.223280971440388131399634898721.dcm 304 [0.00, 127.0.0.1:58005]"}
{"time": "2025-05-21 10:48:57,838", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.218387194706247505045205169130.dcm 200 [0.04, 127.0.0.1:58005]"}
{"time": "2025-05-21 10:48:57,857", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.241073611031000247506142606387.dcm 200 [0.02, 127.0.0.1:58004]"}
{"time": "2025-05-21 10:48:57,908", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.136943255924913899762603730997.dcm 200 [0.02, 127.0.0.1:58004]"}
{"time": "2025-05-21 10:48:58,088", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.301905642463494729146015070819.dcm 200 [0.02, 127.0.0.1:58004]"}
{"time": "2025-05-21 10:49:07,147", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP OPTIONS /api/v1/series/segmentation/new/ 200 [0.00, 127.0.0.1:58004]"}
{"time": "2025-05-21 10:49:07,169", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 172, "message": "HTTP POST /api/v1/series/segmentation/new/ 304 [0.02, 127.0.0.1:58004]"}
{"time": "2025-05-21 10:49:07,174", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP OPTIONS /api/v1/series/segmentation/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/?algorithm=1 200 [0.00, 127.0.0.1:58004]"}
{"time": "2025-05-21 10:49:07,195", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/series/segmentation/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/?algorithm=1 200 [0.02, 127.0.0.1:58004]"}
{"time": "2025-05-21 10:49:07,201", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP OPTIONS /api/v1/series/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/slices/params/ 200 [0.00, 127.0.0.1:58004]"}
{"time": "2025-05-21 10:49:07,219", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/series/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/slices/params/ 200 [0.01, 127.0.0.1:58004]"}
{"time": "2025-05-21 15:04:11,025", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP OPTIONS /api/v1/series/classification/new/?patient_id=2&series_instance_uid=1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805 200 [0.00, 127.0.0.1:59626]"}
{"time": "2025-05-21 15:04:41,377", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/series/classification/new/?patient_id=2&series_instance_uid=1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805 200 [30.35, 127.0.0.1:59626]"}
{"time": "2025-05-21 15:36:04,204", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP OPTIONS /api/v1/authentication/token/ 200 [0.00, 127.0.0.1:59774]"}
{"time": "2025-05-21 15:36:05,222", "level": "WARNING", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 178, "message": "HTTP POST /api/v1/authentication/token/ 401 [1.01, 127.0.0.1:59774]"}
{"time": "2025-05-21 15:36:09,878", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP POST /api/v1/authentication/token/ 200 [1.00, 127.0.0.1:59774]"}
{"time": "2025-05-21 15:36:09,916", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP OPTIONS /api/v1/exams/ 200 [0.00, 127.0.0.1:59774]"}
{"time": "2025-05-21 15:36:09,943", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/exams/ 200 [0.02, 127.0.0.1:59774]"}
{"time": "2025-05-21 15:36:09,963", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/exams/ 200 [0.02, 127.0.0.1:59774]"}
{"time": "2025-05-21 15:47:48,320", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP POST /api/v1/exams/ 201 [18.83, 127.0.0.1:59927]"}
{"time": "2025-05-21 15:47:48,383", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/exams/ 200 [0.06, 127.0.0.1:59927]"}
{"time": "2025-05-21 15:47:50,556", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.1.dcm 200 [0.02, 127.0.0.1:59927]"}
{"time": "2025-05-21 15:47:51,741", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.2.dcm 200 [0.03, 127.0.0.1:59927]"}
{"time": "2025-05-21 15:47:51,774", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.3.dcm 200 [0.01, 127.0.0.1:59927]"}
{"time": "2025-05-21 15:47:51,825", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.4.dcm 200 [0.01, 127.0.0.1:59927]"}
{"time": "2025-05-21 15:47:51,872", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.5.dcm 200 [0.02, 127.0.0.1:59927]"}
{"time": "2025-05-21 15:47:51,932", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.6.dcm 200 [0.01, 127.0.0.1:59927]"}
{"time": "2025-05-21 15:47:51,983", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.7.dcm 200 [0.01, 127.0.0.1:59927]"}
{"time": "2025-05-21 15:47:52,096", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.8.dcm 200 [0.01, 127.0.0.1:59927]"}
{"time": "2025-05-21 15:47:52,529", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.9.dcm 200 [0.01, 127.0.0.1:59927]"}
{"time": "2025-05-21 15:47:52,584", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.10.dcm 200 [0.01, 127.0.0.1:59927]"}
{"time": "2025-05-21 15:47:52,615", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.11.dcm 200 [0.01, 127.0.0.1:59927]"}
{"time": "2025-05-21 15:47:52,705", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.13.dcm 200 [0.01, 127.0.0.1:59929]"}
{"time": "2025-05-21 15:47:52,752", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.14.dcm 200 [0.01, 127.0.0.1:59929]"}
{"time": "2025-05-21 15:47:52,763", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.15.dcm 200 [0.02, 127.0.0.1:59930]"}
{"time": "2025-05-21 15:47:52,785", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.16.dcm 200 [0.01, 127.0.0.1:59930]"}
{"time": "2025-05-21 15:47:52,824", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.17.dcm 200 [0.01, 127.0.0.1:59930]"}
{"time": "2025-05-21 15:47:53,198", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.18.dcm 200 [0.02, 127.0.0.1:59930]"}
{"time": "2025-05-21 15:47:53,213", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.19.dcm 200 [0.01, 127.0.0.1:59930]"}
{"time": "2025-05-21 15:47:53,237", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.20.dcm 200 [0.01, 127.0.0.1:59930]"}
{"time": "2025-05-21 15:47:54,662", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.12.dcm 200 [2.01, 127.0.0.1:59927]"}
{"time": "2025-05-21 15:47:54,681", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.25.dcm 200 [0.04, 127.0.0.1:59933]"}
{"time": "2025-05-21 15:47:54,682", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.24.dcm 200 [0.04, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:47:54,684", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.21.dcm 200 [0.04, 127.0.0.1:59930]"}
{"time": "2025-05-21 15:47:54,684", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.23.dcm 200 [0.04, 127.0.0.1:59931]"}
{"time": "2025-05-21 15:47:54,690", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.22.dcm 200 [0.05, 127.0.0.1:59929]"}
{"time": "2025-05-21 15:47:54,700", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.26.dcm 200 [0.03, 127.0.0.1:59927]"}
{"time": "2025-05-21 15:47:54,718", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.28.dcm 200 [0.03, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:47:54,722", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.31.dcm 200 [0.03, 127.0.0.1:59929]"}
{"time": "2025-05-21 15:47:54,725", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.29.dcm 200 [0.04, 127.0.0.1:59930]"}
{"time": "2025-05-21 15:47:54,727", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.30.dcm 200 [0.04, 127.0.0.1:59931]"}
{"time": "2025-05-21 15:47:54,732", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.27.dcm 200 [0.04, 127.0.0.1:59933]"}
{"time": "2025-05-21 15:47:54,751", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.32.dcm 200 [0.05, 127.0.0.1:59927]"}
{"time": "2025-05-21 15:47:54,774", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.36.dcm 200 [0.04, 127.0.0.1:59931]"}
{"time": "2025-05-21 15:47:54,776", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.35.dcm 200 [0.04, 127.0.0.1:59930]"}
{"time": "2025-05-21 15:47:54,778", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.33.dcm 200 [0.06, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:47:54,788", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.37.dcm 200 [0.05, 127.0.0.1:59933]"}
{"time": "2025-05-21 15:47:54,794", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.34.dcm 200 [0.06, 127.0.0.1:59929]"}
{"time": "2025-05-21 15:47:54,800", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.38.dcm 200 [0.03, 127.0.0.1:59927]"}
{"time": "2025-05-21 15:47:54,808", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.39.dcm 200 [0.03, 127.0.0.1:59931]"}
{"time": "2025-05-21 15:47:54,809", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.40.dcm 200 [0.03, 127.0.0.1:59930]"}
{"time": "2025-05-21 15:47:54,810", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.41.dcm 200 [0.03, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:47:55,260", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.42.dcm 200 [0.02, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:47:55,285", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.43.dcm 200 [0.02, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:47:55,300", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.44.dcm 200 [0.02, 127.0.0.1:59930]"}
{"time": "2025-05-21 15:47:55,317", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.45.dcm 200 [0.02, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:47:55,336", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.46.dcm 200 [0.02, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:47:55,352", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.47.dcm 200 [0.02, 127.0.0.1:59930]"}
{"time": "2025-05-21 15:47:55,415", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.48.dcm 200 [0.03, 127.0.0.1:59930]"}
{"time": "2025-05-21 15:47:57,819", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.49.dcm 200 [0.02, 127.0.0.1:59930]"}
{"time": "2025-05-21 15:47:57,844", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.50.dcm 200 [0.02, 127.0.0.1:59930]"}
{"time": "2025-05-21 15:47:57,858", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.51.dcm 200 [0.02, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:47:57,874", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.52.dcm 200 [0.02, 127.0.0.1:59930]"}
{"time": "2025-05-21 15:47:57,886", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.53.dcm 200 [0.02, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:47:57,891", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.54.dcm 200 [0.01, 127.0.0.1:59930]"}
{"time": "2025-05-21 15:47:57,919", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.55.dcm 200 [0.01, 127.0.0.1:59930]"}
{"time": "2025-05-21 15:47:57,932", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.57.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:47:57,963", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.58.dcm 200 [0.02, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:47:57,983", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.60.dcm 200 [0.02, 127.0.0.1:59930]"}
{"time": "2025-05-21 15:47:57,989", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.61.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:47:58,065", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.62.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:47:58,121", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.63.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:47:58,153", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.64.dcm 200 [0.02, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:47:58,162", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.65.dcm 200 [0.01, 127.0.0.1:59930]"}
{"time": "2025-05-21 15:47:58,186", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.66.dcm 200 [0.01, 127.0.0.1:59930]"}
{"time": "2025-05-21 15:47:58,225", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.67.dcm 200 [0.01, 127.0.0.1:59930]"}
{"time": "2025-05-21 15:47:58,257", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.68.dcm 200 [0.02, 127.0.0.1:59930]"}
{"time": "2025-05-21 15:47:58,270", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.70.dcm 200 [0.02, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:47:58,292", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.71.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:47:58,314", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.73.dcm 200 [0.02, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:47:58,320", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.74.dcm 200 [0.01, 127.0.0.1:59930]"}
{"time": "2025-05-21 15:47:58,337", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.75.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:47:58,353", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.77.dcm 200 [0.02, 127.0.0.1:59930]"}
{"time": "2025-05-21 15:47:58,367", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.79.dcm 200 [0.02, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:47:58,379", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.81.dcm 200 [0.02, 127.0.0.1:59930]"}
{"time": "2025-05-21 15:47:58,389", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.82.dcm 200 [0.02, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:47:58,405", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.84.dcm 200 [0.02, 127.0.0.1:59930]"}
{"time": "2025-05-21 15:47:58,417", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.85.dcm 200 [0.02, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:47:58,433", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.86.dcm 200 [0.02, 127.0.0.1:59930]"}
{"time": "2025-05-21 15:47:58,446", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.87.dcm 200 [0.02, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:47:58,461", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.89.dcm 200 [0.02, 127.0.0.1:59930]"}
{"time": "2025-05-21 15:47:58,472", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.91.dcm 200 [0.02, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:47:58,481", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.92.dcm 200 [0.01, 127.0.0.1:59930]"}
{"time": "2025-05-21 15:47:58,495", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.93.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:47:58,519", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.94.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:47:58,532", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.95.dcm 200 [0.01, 127.0.0.1:59930]"}
{"time": "2025-05-21 15:47:58,561", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.97.dcm 200 [0.01, 127.0.0.1:59930]"}
{"time": "2025-05-21 15:47:58,591", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.98.dcm 200 [0.02, 127.0.0.1:59930]"}
{"time": "2025-05-21 15:47:58,684", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.99.dcm 200 [0.02, 127.0.0.1:59930]"}
{"time": "2025-05-21 15:47:58,699", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.100.dcm 200 [0.02, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:47:58,711", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.101.dcm 200 [0.02, 127.0.0.1:59930]"}
{"time": "2025-05-21 15:47:58,737", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.102.dcm 200 [0.02, 127.0.0.1:59930]"}
{"time": "2025-05-21 15:47:58,759", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.103.dcm 200 [0.01, 127.0.0.1:59930]"}
{"time": "2025-05-21 15:47:58,774", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.104.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:47:58,798", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.105.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:47:58,825", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.106.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:48:00,481", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP OPTIONS /api/v1/series/segmentation/new/ 200 [0.00, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:14,029", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP POST /api/v1/series/segmentation/new/ 201 [193.55, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:14,040", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP OPTIONS /api/v1/series/segmentation/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/?algorithm=1 200 [0.00, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:14,065", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/series/segmentation/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/?algorithm=1 200 [0.02, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:14,069", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP OPTIONS /api/v1/series/segmentation/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/?algorithm=1&page=2 200 [0.00, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:14,093", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/series/segmentation/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/?algorithm=1&page=2 200 [0.02, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:14,097", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP OPTIONS /api/v1/series/segmentation/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/?algorithm=1&page=3 200 [0.00, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:14,121", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/series/segmentation/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/?algorithm=1&page=3 200 [0.02, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:14,126", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP OPTIONS /api/v1/series/segmentation/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/?algorithm=1&page=4 200 [0.00, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:14,402", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/series/segmentation/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/?algorithm=1&page=4 200 [0.27, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:14,406", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP OPTIONS /api/v1/series/segmentation/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/?algorithm=1&page=5 200 [0.00, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:14,435", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/series/segmentation/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/?algorithm=1&page=5 200 [0.03, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:14,440", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP OPTIONS /api/v1/series/segmentation/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/?algorithm=1&page=6 200 [0.00, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:14,463", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/series/segmentation/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/?algorithm=1&page=6 200 [0.02, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:14,467", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP OPTIONS /api/v1/series/segmentation/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/?algorithm=1&page=7 200 [0.00, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:14,491", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/series/segmentation/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/?algorithm=1&page=7 200 [0.02, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:14,496", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP OPTIONS /api/v1/series/segmentation/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/?algorithm=1&page=8 200 [0.00, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:14,519", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/series/segmentation/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/?algorithm=1&page=8 200 [0.02, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:14,524", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP OPTIONS /api/v1/series/segmentation/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/?algorithm=1&page=9 200 [0.00, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:14,556", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/series/segmentation/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/?algorithm=1&page=9 200 [0.03, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:14,560", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP OPTIONS /api/v1/series/segmentation/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/?algorithm=1&page=10 200 [0.00, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:14,580", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/series/segmentation/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/?algorithm=1&page=10 200 [0.02, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:14,585", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP OPTIONS /api/v1/series/segmentation/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/?algorithm=1&page=11 200 [0.00, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:14,603", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/series/segmentation/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/?algorithm=1&page=11 200 [0.02, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:14,608", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP OPTIONS /api/v1/series/segmentation/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/?algorithm=1&page=12 200 [0.00, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:14,628", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/series/segmentation/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/?algorithm=1&page=12 200 [0.02, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:14,632", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP OPTIONS /api/v1/series/segmentation/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/?algorithm=1&page=13 200 [0.00, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:14,651", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/series/segmentation/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/?algorithm=1&page=13 200 [0.02, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:14,660", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP OPTIONS /api/v1/series/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/slices/params/ 200 [0.00, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:14,677", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/series/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/slices/params/ 200 [0.02, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:14,682", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP OPTIONS /api/v1/series/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/slices/params/?page=2 200 [0.00, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:14,698", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/series/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/slices/params/?page=2 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:14,702", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP OPTIONS /api/v1/series/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/slices/params/?page=3 200 [0.00, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:14,718", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/series/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/slices/params/?page=3 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:14,722", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP OPTIONS /api/v1/series/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/slices/params/?page=4 200 [0.00, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:14,738", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/series/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/slices/params/?page=4 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:14,743", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP OPTIONS /api/v1/series/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/slices/params/?page=5 200 [0.00, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:14,758", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/series/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/slices/params/?page=5 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:14,762", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP OPTIONS /api/v1/series/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/slices/params/?page=6 200 [0.00, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:14,777", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/series/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/slices/params/?page=6 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:14,782", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP OPTIONS /api/v1/series/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/slices/params/?page=7 200 [0.00, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:14,798", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/series/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/slices/params/?page=7 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:14,806", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP OPTIONS /api/v1/series/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/slices/params/?page=8 200 [0.00, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:14,823", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/series/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/slices/params/?page=8 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:14,833", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP OPTIONS /api/v1/series/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/slices/params/?page=9 200 [0.00, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:14,848", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/series/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/slices/params/?page=9 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:14,855", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP OPTIONS /api/v1/series/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/slices/params/?page=10 200 [0.00, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:14,873", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/series/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/slices/params/?page=10 200 [0.02, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:14,882", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP OPTIONS /api/v1/series/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/slices/params/?page=11 200 [0.00, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:14,901", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/series/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/slices/params/?page=11 200 [0.02, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:14,907", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP OPTIONS /api/v1/series/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/slices/params/?page=12 200 [0.00, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:14,922", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/series/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/slices/params/?page=12 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:14,927", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP OPTIONS /api/v1/series/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/slices/params/?page=13 200 [0.00, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:14,943", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/series/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/slices/params/?page=13 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:15,086", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.56.dcm 200 [0.00, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:15,139", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.59.dcm 200 [0.00, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:15,332", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.69.dcm 200 [0.00, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:15,390", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.72.dcm 200 [0.00, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:15,476", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.76.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:15,538", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.78.dcm 200 [0.00, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:15,590", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.80.dcm 200 [0.00, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:15,667", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.83.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:15,794", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.88.dcm 200 [0.00, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:15,852", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.90.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:15,978", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.96.dcm 200 [0.00, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:16,234", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.107.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:16,268", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.108.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:16,298", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.109.dcm 200 [0.00, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:16,326", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.110.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:16,355", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.111.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:16,389", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.112.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:16,424", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.113.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:16,455", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.114.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:16,491", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.115.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:16,527", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.116.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:16,558", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.117.dcm 200 [0.00, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:16,589", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.118.dcm 200 [0.00, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:16,619", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.119.dcm 200 [0.00, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:16,649", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.120.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:16,679", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.121.dcm 200 [0.00, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:16,711", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.122.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:16,741", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.123.dcm 200 [0.00, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:16,774", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.124.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:16,807", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.125.dcm 200 [0.00, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:16,838", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.126.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:16,867", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.127.dcm 200 [0.00, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:16,897", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.128.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:16,942", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.129.dcm 200 [0.02, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:16,975", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.130.dcm 200 [0.00, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:17,008", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.131.dcm 200 [0.00, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:17,049", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.132.dcm 200 [0.02, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:17,092", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.133.dcm 200 [0.02, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:17,128", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.134.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:17,164", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.135.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:17,200", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.136.dcm 200 [0.00, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:17,233", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.137.dcm 200 [0.00, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:17,280", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.138.dcm 200 [0.02, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:17,323", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.139.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:17,354", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.140.dcm 200 [0.00, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:17,385", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.141.dcm 200 [0.00, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:17,414", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.142.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:17,445", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.143.dcm 200 [0.00, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:17,479", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.144.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:17,511", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.145.dcm 200 [0.00, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:17,540", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.146.dcm 200 [0.00, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:17,577", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.147.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:17,611", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.148.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:17,643", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.149.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:17,676", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.150.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:17,707", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.151.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:17,740", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.152.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:17,770", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.153.dcm 200 [0.00, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:17,801", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.154.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:17,828", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.155.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:17,857", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.156.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:17,882", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.157.dcm 200 [0.00, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:17,906", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.158.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:17,931", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.159.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:17,956", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.160.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:17,981", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.161.dcm 200 [0.00, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:18,008", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.162.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:18,033", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.163.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:18,057", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.164.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:18,079", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.165.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:18,101", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.166.dcm 200 [0.00, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:18,126", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.167.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:18,148", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.168.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:18,175", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.169.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:18,200", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.170.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:18,223", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.171.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:18,247", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.172.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:18,270", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.173.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:18,292", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.174.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:18,314", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.175.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:18,342", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.176.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:18,369", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.177.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:18,393", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.178.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:18,422", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.179.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:18,445", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.180.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:18,467", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.181.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:18,490", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.182.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:18,515", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.183.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:18,550", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.184.dcm 200 [0.02, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:18,591", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.185.dcm 200 [0.02, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:18,631", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.186.dcm 200 [0.02, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:18,654", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.187.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:18,676", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.188.dcm 200 [0.00, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:18,697", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.189.dcm 200 [0.00, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:18,721", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.190.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:18,743", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.191.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:18,765", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.192.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:18,787", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.193.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:18,807", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.194.dcm 200 [0.00, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:18,828", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.195.dcm 200 [0.00, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:18,849", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.196.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:18,867", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.197.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:18,886", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.198.dcm 200 [0.00, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:18,906", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.199.dcm 200 [0.00, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:18,926", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.200.dcm 200 [0.00, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:18,953", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.201.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:18,977", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.202.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:18,997", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.203.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:19,018", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.204.dcm 200 [0.00, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:19,044", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.205.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:19,068", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.206.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:19,087", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.207.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:19,107", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.208.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:19,127", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.209.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:19,147", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.210.dcm 200 [0.00, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:19,165", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.211.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:19,185", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.212.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:19,203", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.213.dcm 200 [0.00, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:19,221", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.214.dcm 200 [0.00, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:19,244", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.215.dcm 200 [0.00, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:19,264", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.216.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:19,285", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.217.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:19,304", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.218.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:19,326", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.219.dcm 200 [0.00, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:19,346", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.220.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:19,365", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.221.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:19,384", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.222.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:19,404", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.223.dcm 200 [0.00, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:19,424", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.224.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:19,442", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.225.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:19,458", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.226.dcm 200 [0.00, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:19,478", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.227.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:19,507", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.228.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:19,524", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.229.dcm 200 [0.00, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:19,543", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.230.dcm 200 [0.00, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:19,565", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.231.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:19,584", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.232.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:19,602", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.233.dcm 200 [0.00, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:19,622", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.234.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:19,642", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.235.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:19,660", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.236.dcm 200 [0.00, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:19,679", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.237.dcm 200 [0.00, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:19,699", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.238.dcm 200 [0.00, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:19,719", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.239.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:19,741", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.240.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:19,760", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.241.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:19,780", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.242.dcm 200 [0.00, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:19,805", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.243.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:19,828", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.244.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:19,859", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.245.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:19,897", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.246.dcm 200 [0.02, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:19,923", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.247.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:19,948", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.248.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:19,971", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.249.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:19,993", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.250.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:20,018", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.251.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:20,038", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.252.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:20,057", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.253.dcm 200 [0.00, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:20,075", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.254.dcm 200 [0.00, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:20,094", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.255.dcm 200 [0.00, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:20,115", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.256.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-21 15:51:20,138", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.3/1.2.840.113619.2.411.3.168459033.994.1618394979.196.257.dcm 200 [0.01, 127.0.0.1:59932]"}
{"time": "2025-05-27 19:05:42,058", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP OPTIONS /api/v1/exams/ 200 [0.05, 127.0.0.1:56148]"}
{"time": "2025-05-27 19:05:42,139", "level": "WARNING", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 178, "message": "HTTP GET /api/v1/exams/ 401 [0.06, 127.0.0.1:56148]"}
{"time": "2025-05-27 19:05:48,373", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP OPTIONS /api/v1/authentication/token/ 200 [0.00, 127.0.0.1:56155]"}
{"time": "2025-05-27 19:05:49,485", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP POST /api/v1/authentication/token/ 200 [1.09, 127.0.0.1:56155]"}
{"time": "2025-05-27 19:05:49,636", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/exams/ 200 [0.14, 127.0.0.1:56155]"}
{"time": "2025-05-27 19:05:49,707", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/exams/ 200 [0.07, 127.0.0.1:56156]"}
{"time": "2025-05-27 19:05:51,465", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 172, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.911257607240619696148712587012.dcm 304 [0.02, 127.0.0.1:56156]"}
{"time": "2025-05-27 19:05:54,451", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP OPTIONS /api/v1/series/segmentation/new/ 200 [0.01, 127.0.0.1:56157]"}
{"time": "2025-05-27 19:05:54,500", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 172, "message": "HTTP POST /api/v1/series/segmentation/new/ 304 [0.05, 127.0.0.1:56157]"}
{"time": "2025-05-27 19:05:54,504", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP OPTIONS /api/v1/series/segmentation/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/?algorithm=1 200 [0.00, 127.0.0.1:56157]"}
{"time": "2025-05-27 19:05:54,530", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/series/segmentation/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/?algorithm=1 200 [0.03, 127.0.0.1:56157]"}
{"time": "2025-05-27 19:05:54,536", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP OPTIONS /api/v1/series/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/slices/params/ 200 [0.00, 127.0.0.1:56157]"}
{"time": "2025-05-27 19:05:54,551", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 172, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.184801338381895107635381026857.dcm 304 [0.01, 127.0.0.1:56158]"}
{"time": "2025-05-27 19:05:54,565", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/series/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/slices/params/ 200 [0.03, 127.0.0.1:56157]"}
{"time": "2025-05-27 19:05:54,569", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 172, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.227252232966919521559290115017.dcm 304 [0.01, 127.0.0.1:56158]"}
{"time": "2025-05-27 19:05:54,584", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 172, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.305763411933569638615761005863.dcm 304 [0.00, 127.0.0.1:56158]"}
{"time": "2025-05-27 19:05:54,602", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 172, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.892649213333065045357131733969.dcm 304 [0.01, 127.0.0.1:56158]"}
{"time": "2025-05-27 19:05:54,618", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 172, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.210616673074470338920284866510.dcm 304 [0.01, 127.0.0.1:56158]"}
{"time": "2025-05-27 19:05:54,635", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 172, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.308998466994580007520425874686.dcm 304 [0.00, 127.0.0.1:56158]"}
{"time": "2025-05-27 19:05:54,650", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 172, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.254916896710732470846876971852.dcm 304 [0.01, 127.0.0.1:56158]"}
{"time": "2025-05-27 19:05:54,665", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 172, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.255571364135736589219701492950.dcm 304 [0.00, 127.0.0.1:56158]"}
{"time": "2025-05-27 19:05:54,682", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 172, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.147995027728672345689485008579.dcm 304 [0.01, 127.0.0.1:56158]"}
{"time": "2025-05-27 19:05:54,698", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 172, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.204716343021109697619551710230.dcm 304 [0.00, 127.0.0.1:56158]"}
{"time": "2025-05-27 19:05:54,720", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 172, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.283914737330506508971730533367.dcm 304 [0.01, 127.0.0.1:56158]"}
{"time": "2025-05-27 19:05:54,741", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 172, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.184131899543495374569818432657.dcm 304 [0.01, 127.0.0.1:56158]"}
{"time": "2025-05-27 19:05:54,765", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 172, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.264364194296293440320787350583.dcm 304 [0.01, 127.0.0.1:56158]"}
{"time": "2025-05-27 19:05:54,793", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 172, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.216515627058008697217695696166.dcm 304 [0.01, 127.0.0.1:56158]"}
{"time": "2025-05-27 19:05:54,814", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 172, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.223280971440388131399634898721.dcm 304 [0.01, 127.0.0.1:56158]"}
{"time": "2025-05-27 19:05:54,853", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.218387194706247505045205169130.dcm 200 [0.02, 127.0.0.1:56158]"}
{"time": "2025-05-27 19:05:54,895", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.241073611031000247506142606387.dcm 200 [0.01, 127.0.0.1:56158]"}
{"time": "2025-05-27 19:05:54,937", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.136943255924913899762603730997.dcm 200 [0.02, 127.0.0.1:56158]"}
{"time": "2025-05-27 19:05:54,973", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.301905642463494729146015070819.dcm 200 [0.01, 127.0.0.1:56158]"}
{"time": "2025-05-27 19:06:47,831", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/exams/ 200 [0.07, 127.0.0.1:56186]"}
{"time": "2025-05-27 19:06:51,298", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 172, "message": "HTTP POST /api/v1/series/segmentation/new/ 304 [0.02, 127.0.0.1:56189]"}
{"time": "2025-05-27 19:06:51,324", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/series/segmentation/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/?algorithm=1 200 [0.02, 127.0.0.1:56189]"}
{"time": "2025-05-27 19:06:51,346", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/series/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/slices/params/ 200 [0.02, 127.0.0.1:56189]"}
{"time": "2025-05-30 11:46:53,278", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP OPTIONS /api/v1/exams/ 200 [0.01, 127.0.0.1:64240]"}
{"time": "2025-05-30 11:46:53,302", "level": "WARNING", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 178, "message": "HTTP GET /api/v1/exams/ 401 [0.02, 127.0.0.1:64240]"}
{"time": "2025-05-30 11:47:05,400", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP OPTIONS /api/v1/authentication/token/ 200 [0.00, 127.0.0.1:64240]"}
{"time": "2025-05-30 11:47:06,506", "level": "WARNING", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 178, "message": "HTTP POST /api/v1/authentication/token/ 401 [1.10, 127.0.0.1:64240]"}
{"time": "2025-05-30 11:47:07,407", "level": "WARNING", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 178, "message": "HTTP POST /api/v1/authentication/token/ 401 [1.04, 127.0.0.1:64244]"}
{"time": "2025-05-30 11:47:43,649", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP OPTIONS /api/v1/authentication/register/ 200 [0.00, 127.0.0.1:64244]"}
{"time": "2025-05-30 11:47:44,868", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP POST /api/v1/authentication/register/ 201 [1.22, 127.0.0.1:64244]"}
{"time": "2025-05-30 11:48:07,774", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP POST /api/v1/authentication/token/ 200 [1.05, 127.0.0.1:64244]"}
{"time": "2025-05-30 11:48:07,822", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/exams/ 200 [0.01, 127.0.0.1:64244]"}
{"time": "2025-05-30 11:48:07,851", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/exams/ 200 [0.02, 127.0.0.1:64244]"}
{"time": "2025-05-30 11:48:27,218", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP POST /api/v1/exams/ 201 [8.68, 127.0.0.1:64244]"}
{"time": "2025-05-30 11:48:27,276", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/exams/ 200 [0.05, 127.0.0.1:64244]"}
{"time": "2025-05-30 11:55:03,635", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP OPTIONS /api/v1/series/segmentation/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/ 200 [0.00, 127.0.0.1:64376]"}
{"time": "2025-05-30 11:55:03,657", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/series/segmentation/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/ 200 [0.02, 127.0.0.1:64376]"}
{"time": "2025-05-30 11:55:03,658", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.1.dcm 200 [0.02, 127.0.0.1:64377]"}
{"time": "2025-05-30 11:55:03,662", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP OPTIONS /api/v1/series/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/params/ 200 [0.00, 127.0.0.1:64376]"}
{"time": "2025-05-30 11:55:03,675", "level": "WARNING", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 176, "message": "HTTP GET /api/v1/series/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/params/ 404 [0.01, 127.0.0.1:64376]"}
{"time": "2025-05-30 11:55:03,680", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP OPTIONS /api/v1/series/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/slices/params/ 200 [0.00, 127.0.0.1:64376]"}
{"time": "2025-05-30 11:55:03,690", "level": "WARNING", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 176, "message": "HTTP GET /api/v1/series/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/slices/params/ 404 [0.01, 127.0.0.1:64376]"}
{"time": "2025-05-30 11:55:13,515", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.148.dcm 200 [0.01, 127.0.0.1:64376]"}
{"time": "2025-05-30 11:55:29,136", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/exams/ 200 [0.07, 127.0.0.1:64376]"}
{"time": "2025-05-30 11:55:34,041", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP OPTIONS /api/v1/series/segmentation/new/ 200 [0.00, 127.0.0.1:64376]"}
{"time": "2025-05-30 11:55:34,049", "level": "WARNING", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 178, "message": "HTTP POST /api/v1/series/segmentation/new/ 400 [0.01, 127.0.0.1:64376]"}
{"time": "2025-05-30 11:55:35,028", "level": "WARNING", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 178, "message": "HTTP POST /api/v1/series/segmentation/new/ 400 [0.02, 127.0.0.1:64376]"}
{"time": "2025-05-30 11:55:36,103", "level": "WARNING", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 178, "message": "HTTP POST /api/v1/series/segmentation/new/ 400 [0.01, 127.0.0.1:64376]"}
{"time": "2025-05-30 11:55:36,269", "level": "WARNING", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 178, "message": "HTTP POST /api/v1/series/segmentation/new/ 400 [0.01, 127.0.0.1:64376]"}
{"time": "2025-05-30 11:55:38,642", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/series/segmentation/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/ 200 [0.03, 127.0.0.1:64376]"}
{"time": "2025-05-30 11:55:38,655", "level": "WARNING", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 176, "message": "HTTP GET /api/v1/series/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/params/ 404 [0.01, 127.0.0.1:64376]"}
{"time": "2025-05-30 11:55:38,668", "level": "WARNING", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 176, "message": "HTTP GET /api/v1/series/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/slices/params/ 404 [0.01, 127.0.0.1:64376]"}
{"time": "2025-05-30 11:55:45,581", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.2.dcm 200 [0.02, 127.0.0.1:64376]"}
{"time": "2025-05-30 11:55:45,639", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.3.dcm 200 [0.01, 127.0.0.1:64376]"}
{"time": "2025-05-30 11:55:45,673", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.4.dcm 200 [0.02, 127.0.0.1:64376]"}
{"time": "2025-05-30 11:55:45,705", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.5.dcm 200 [0.02, 127.0.0.1:64376]"}
{"time": "2025-05-30 11:55:45,730", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.6.dcm 200 [0.01, 127.0.0.1:64376]"}
{"time": "2025-05-30 11:55:46,047", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.7.dcm 200 [0.04, 127.0.0.1:64376]"}
{"time": "2025-05-30 11:55:46,062", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.8.dcm 200 [0.04, 127.0.0.1:64377]"}
{"time": "2025-05-30 11:55:47,041", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.9.dcm 200 [0.02, 127.0.0.1:64377]"}
{"time": "2025-05-30 11:55:47,347", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.10.dcm 200 [0.02, 127.0.0.1:64377]"}
{"time": "2025-05-30 11:55:47,869", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.11.dcm 200 [0.07, 127.0.0.1:64377]"}
{"time": "2025-05-30 11:55:47,875", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.12.dcm 200 [0.06, 127.0.0.1:64376]"}
{"time": "2025-05-30 11:55:48,673", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.13.dcm 200 [0.02, 127.0.0.1:64376]"}
{"time": "2025-05-30 11:55:48,679", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.14.dcm 200 [0.01, 127.0.0.1:64377]"}
{"time": "2025-05-30 11:55:48,915", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.15.dcm 200 [0.03, 127.0.0.1:64377]"}
{"time": "2025-05-30 11:55:48,931", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.16.dcm 200 [0.04, 127.0.0.1:64376]"}
{"time": "2025-05-30 11:55:49,104", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.17.dcm 200 [0.01, 127.0.0.1:64376]"}
{"time": "2025-05-30 11:55:49,116", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.18.dcm 200 [0.01, 127.0.0.1:64377]"}
{"time": "2025-05-30 11:55:49,289", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.19.dcm 200 [0.02, 127.0.0.1:64377]"}
{"time": "2025-05-30 11:56:46,002", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/series/segmentation/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/ 200 [0.02, 127.0.0.1:64377]"}
{"time": "2025-05-30 11:56:46,019", "level": "WARNING", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 176, "message": "HTTP GET /api/v1/series/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/params/ 404 [0.01, 127.0.0.1:64377]"}
{"time": "2025-05-30 11:56:46,032", "level": "WARNING", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 176, "message": "HTTP GET /api/v1/series/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/slices/params/ 404 [0.01, 127.0.0.1:64377]"}
{"time": "2025-05-30 11:56:58,992", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/series/segmentation/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/ 200 [0.02, 127.0.0.1:64377]"}
{"time": "2025-05-30 11:56:59,003", "level": "WARNING", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 176, "message": "HTTP GET /api/v1/series/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/params/ 404 [0.01, 127.0.0.1:64377]"}
{"time": "2025-05-30 11:56:59,016", "level": "WARNING", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 176, "message": "HTTP GET /api/v1/series/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/slices/params/ 404 [0.01, 127.0.0.1:64377]"}
{"time": "2025-05-30 12:02:35,778", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.20.dcm 200 [0.03, 127.0.0.1:64436]"}
{"time": "2025-05-30 12:02:36,240", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.21.dcm 200 [0.02, 127.0.0.1:64436]"}
{"time": "2025-05-30 12:02:36,637", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.22.dcm 200 [0.02, 127.0.0.1:64436]"}
{"time": "2025-05-30 12:03:15,117", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.23.dcm 200 [0.03, 127.0.0.1:64436]"}
{"time": "2025-05-30 12:03:15,169", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.24.dcm 200 [0.02, 127.0.0.1:64436]"}
{"time": "2025-05-30 12:03:15,651", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.25.dcm 200 [0.03, 127.0.0.1:64436]"}
{"time": "2025-05-30 12:03:15,660", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.26.dcm 200 [0.02, 127.0.0.1:64439]"}
{"time": "2025-05-30 12:03:16,258", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.27.dcm 200 [0.02, 127.0.0.1:64439]"}
{"time": "2025-05-30 12:03:16,855", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.28.dcm 200 [0.02, 127.0.0.1:64439]"}
{"time": "2025-05-30 12:03:16,860", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.29.dcm 200 [0.01, 127.0.0.1:64436]"}
{"time": "2025-05-30 12:03:17,423", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.30.dcm 200 [0.04, 127.0.0.1:64436]"}
{"time": "2025-05-30 12:03:24,036", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.32.dcm 200 [0.03, 127.0.0.1:64436]"}
{"time": "2025-05-30 12:03:24,299", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.33.dcm 200 [0.02, 127.0.0.1:64436]"}
{"time": "2025-05-30 12:03:24,513", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.35.dcm 200 [0.02, 127.0.0.1:64436]"}
{"time": "2025-05-30 12:03:24,870", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.37.dcm 200 [0.07, 127.0.0.1:64436]"}
{"time": "2025-05-30 12:03:25,350", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.40.dcm 200 [0.02, 127.0.0.1:64436]"}
{"time": "2025-05-30 12:03:25,858", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.41.dcm 200 [0.01, 127.0.0.1:64436]"}
{"time": "2025-05-30 12:03:26,189", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.42.dcm 200 [0.02, 127.0.0.1:64436]"}
{"time": "2025-05-30 12:03:26,881", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.43.dcm 200 [0.02, 127.0.0.1:64436]"}
{"time": "2025-05-30 12:03:32,156", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.31.dcm 200 [0.03, 127.0.0.1:64436]"}
{"time": "2025-05-30 12:03:32,864", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.39.dcm 200 [0.04, 127.0.0.1:64436]"}
{"time": "2025-05-30 15:09:29,845", "level": "INFO", "logger": "django.utils.autoreload", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\django\utils\autoreload.py", "line": 667, "message": "Watching for file changes with StatReloader"}
{"time": "2025-05-30 15:09:46,668", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP OPTIONS /api/v1/exams/ 200 [0.00, 127.0.0.1:50344]"}
{"time": "2025-05-30 15:09:46,677", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP OPTIONS /api/v1/exams/ 200 [0.00, 127.0.0.1:50345]"}
{"time": "2025-05-30 15:09:46,756", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/exams/ 200 [0.08, 127.0.0.1:50344]"}
{"time": "2025-05-30 15:09:46,798", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/exams/ 200 [0.04, 127.0.0.1:50345]"}
{"time": "2025-05-30 15:09:49,282", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP OPTIONS /api/v1/series/segmentation/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/ 200 [0.00, 127.0.0.1:50345]"}
{"time": "2025-05-30 15:09:49,319", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/series/segmentation/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/ 200 [0.03, 127.0.0.1:50345]"}
{"time": "2025-05-30 15:09:49,323", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP OPTIONS /api/v1/series/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/params/ 200 [0.00, 127.0.0.1:50345]"}
{"time": "2025-05-30 15:09:49,335", "level": "WARNING", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 176, "message": "HTTP GET /api/v1/series/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/params/ 404 [0.01, 127.0.0.1:50345]"}
{"time": "2025-05-30 15:09:49,340", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP OPTIONS /api/v1/series/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/slices/params/ 200 [0.00, 127.0.0.1:50345]"}
{"time": "2025-05-30 15:09:49,352", "level": "WARNING", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 176, "message": "HTTP GET /api/v1/series/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/slices/params/ 404 [0.01, 127.0.0.1:50345]"}
{"time": "2025-05-30 15:09:50,786", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.80.dcm 200 [0.11, 127.0.0.1:50345]"}
{"time": "2025-05-30 15:09:50,794", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.85.dcm 200 [0.02, 127.0.0.1:50353]"}
{"time": "2025-05-30 15:09:50,813", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.87.dcm 200 [0.02, 127.0.0.1:50345]"}
{"time": "2025-05-30 15:09:50,822", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.90.dcm 200 [0.02, 127.0.0.1:50353]"}
{"time": "2025-05-30 15:09:50,840", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.94.dcm 200 [0.02, 127.0.0.1:50345]"}
{"time": "2025-05-30 15:09:50,845", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.97.dcm 200 [0.02, 127.0.0.1:50353]"}
{"time": "2025-05-30 15:09:50,851", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.81.dcm 200 [0.14, 127.0.0.1:50344]"}
{"time": "2025-05-30 15:09:50,868", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.103.dcm 200 [0.02, 127.0.0.1:50345]"}
{"time": "2025-05-30 15:09:50,870", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.83.dcm 200 [0.12, 127.0.0.1:50351]"}
{"time": "2025-05-30 15:09:50,877", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.84.dcm 200 [0.11, 127.0.0.1:50352]"}
{"time": "2025-05-30 15:09:50,885", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.106.dcm 200 [0.02, 127.0.0.1:50344]"}
{"time": "2025-05-30 15:09:50,893", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.111.dcm 200 [0.02, 127.0.0.1:50353]"}
{"time": "2025-05-30 15:09:50,900", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.113.dcm 200 [0.01, 127.0.0.1:50352]"}
{"time": "2025-05-30 15:09:50,920", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.115.dcm 200 [0.02, 127.0.0.1:50351]"}
{"time": "2025-05-30 15:09:50,933", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.116.dcm 200 [0.02, 127.0.0.1:50345]"}
{"time": "2025-05-30 15:09:51,877", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.117.dcm 200 [0.04, 127.0.0.1:50345]"}
{"time": "2025-05-30 15:09:52,319", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.118.dcm 200 [0.03, 127.0.0.1:50345]"}
{"time": "2025-05-30 15:09:52,334", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.119.dcm 200 [0.01, 127.0.0.1:50345]"}
{"time": "2025-05-30 15:09:52,419", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.120.dcm 200 [0.02, 127.0.0.1:50345]"}
{"time": "2025-05-30 15:09:52,457", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.121.dcm 200 [0.01, 127.0.0.1:50345]"}
{"time": "2025-05-30 15:09:52,495", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.122.dcm 200 [0.01, 127.0.0.1:50345]"}
{"time": "2025-05-30 15:09:52,545", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.123.dcm 200 [0.02, 127.0.0.1:50345]"}
{"time": "2025-05-30 15:09:52,859", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.125.dcm 200 [0.02, 127.0.0.1:50351]"}
{"time": "2025-05-30 15:09:52,860", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.124.dcm 200 [0.03, 127.0.0.1:50345]"}
{"time": "2025-05-30 15:09:52,879", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.126.dcm 200 [0.01, 127.0.0.1:50345]"}
{"time": "2025-05-30 15:09:52,896", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.127.dcm 200 [0.01, 127.0.0.1:50345]"}
{"time": "2025-05-30 15:09:52,920", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.128.dcm 200 [0.01, 127.0.0.1:50345]"}
{"time": "2025-05-30 15:09:52,964", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.129.dcm 200 [0.03, 127.0.0.1:50345]"}
{"time": "2025-05-30 15:09:52,969", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.130.dcm 200 [0.02, 127.0.0.1:50351]"}
{"time": "2025-05-30 15:09:53,729", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.137.dcm 200 [0.04, 127.0.0.1:50351]"}
{"time": "2025-05-30 15:09:53,775", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.138.dcm 200 [0.01, 127.0.0.1:50351]"}
{"time": "2025-05-30 15:09:53,807", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.140.dcm 200 [0.02, 127.0.0.1:50351]"}
{"time": "2025-05-30 15:09:53,820", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.141.dcm 200 [0.02, 127.0.0.1:50345]"}
{"time": "2025-05-30 15:09:53,831", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.142.dcm 200 [0.02, 127.0.0.1:50351]"}
{"time": "2025-05-30 15:09:53,844", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.144.dcm 200 [0.01, 127.0.0.1:50345]"}
{"time": "2025-05-30 15:09:53,859", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.146.dcm 200 [0.02, 127.0.0.1:50351]"}
{"time": "2025-05-30 15:09:53,874", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.147.dcm 200 [0.02, 127.0.0.1:50345]"}
{"time": "2025-05-30 15:09:53,886", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.149.dcm 200 [0.02, 127.0.0.1:50351]"}
{"time": "2025-05-30 15:09:53,901", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.152.dcm 200 [0.02, 127.0.0.1:50345]"}
{"time": "2025-05-30 15:09:53,912", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.153.dcm 200 [0.02, 127.0.0.1:50351]"}
{"time": "2025-05-30 15:09:53,940", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.154.dcm 200 [0.02, 127.0.0.1:50351]"}
{"time": "2025-05-30 15:09:58,864", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.155.dcm 200 [0.03, 127.0.0.1:50351]"}
{"time": "2025-05-30 15:09:58,881", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.156.dcm 200 [0.01, 127.0.0.1:50351]"}
{"time": "2025-05-30 15:09:58,894", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.157.dcm 200 [0.01, 127.0.0.1:50351]"}
{"time": "2025-05-30 15:09:58,920", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.158.dcm 200 [0.01, 127.0.0.1:50351]"}
{"time": "2025-05-30 15:09:58,936", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.159.dcm 200 [0.01, 127.0.0.1:50351]"}
{"time": "2025-05-30 15:09:58,982", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.160.dcm 200 [0.02, 127.0.0.1:50351]"}
{"time": "2025-05-30 15:09:59,301", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.161.dcm 200 [0.02, 127.0.0.1:50351]"}
{"time": "2025-05-30 15:09:59,316", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.162.dcm 200 [0.02, 127.0.0.1:50345]"}
{"time": "2025-05-30 15:09:59,338", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.163.dcm 200 [0.02, 127.0.0.1:50345]"}
{"time": "2025-05-30 15:09:59,362", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.164.dcm 200 [0.02, 127.0.0.1:50351]"}
{"time": "2025-05-30 15:09:59,375", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.165.dcm 200 [0.01, 127.0.0.1:50345]"}
{"time": "2025-05-30 15:09:59,418", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.166.dcm 200 [0.01, 127.0.0.1:50345]"}
{"time": "2025-05-30 15:11:07,416", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/series/segmentation/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/ 200 [0.02, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:11:07,432", "level": "WARNING", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 176, "message": "HTTP GET /api/v1/series/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/params/ 404 [0.01, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:11:07,444", "level": "WARNING", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 176, "message": "HTTP GET /api/v1/series/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/slices/params/ 404 [0.01, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:11:24,413", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/series/segmentation/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/ 200 [0.02, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:11:24,441", "level": "WARNING", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 176, "message": "HTTP GET /api/v1/series/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/params/ 404 [0.01, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:11:24,454", "level": "WARNING", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 176, "message": "HTTP GET /api/v1/series/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/slices/params/ 404 [0.01, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:11:51,582", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/exams/ 200 [0.05, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:11:52,957", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/series/segmentation/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/ 200 [0.01, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:11:52,969", "level": "WARNING", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 176, "message": "HTTP GET /api/v1/series/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/params/ 404 [0.01, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:11:52,984", "level": "WARNING", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 176, "message": "HTTP GET /api/v1/series/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/slices/params/ 404 [0.01, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:11:56,861", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/series/segmentation/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/ 200 [0.02, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:11:56,873", "level": "WARNING", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 176, "message": "HTTP GET /api/v1/series/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/params/ 404 [0.01, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:11:56,888", "level": "WARNING", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 176, "message": "HTTP GET /api/v1/series/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/slices/params/ 404 [0.01, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:11:57,181", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.34.dcm 200 [0.02, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:11:57,227", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.36.dcm 200 [0.01, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:11:57,269", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.38.dcm 200 [0.01, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:11:57,382", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.44.dcm 200 [0.02, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:11:57,416", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.45.dcm 200 [0.01, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:11:57,444", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.46.dcm 200 [0.01, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:11:57,473", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.47.dcm 200 [0.01, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:11:57,503", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.48.dcm 200 [0.02, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:11:57,534", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.49.dcm 200 [0.02, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:11:57,561", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.50.dcm 200 [0.01, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:11:57,587", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.51.dcm 200 [0.01, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:11:57,614", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.52.dcm 200 [0.01, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:11:57,647", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.53.dcm 200 [0.02, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:11:57,676", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.54.dcm 200 [0.02, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:11:57,715", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.55.dcm 200 [0.02, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:11:57,746", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.56.dcm 200 [0.02, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:11:57,783", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.57.dcm 200 [0.02, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:11:57,819", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.58.dcm 200 [0.02, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:11:57,851", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.59.dcm 200 [0.02, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:11:57,896", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.60.dcm 200 [0.03, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:11:57,925", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.61.dcm 200 [0.01, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:11:57,961", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.62.dcm 200 [0.02, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:11:57,997", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.63.dcm 200 [0.02, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:11:58,034", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.64.dcm 200 [0.02, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:11:58,075", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.65.dcm 200 [0.03, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:11:58,106", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.66.dcm 200 [0.02, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:11:58,147", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.67.dcm 200 [0.02, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:11:58,177", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.68.dcm 200 [0.02, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:11:58,213", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.69.dcm 200 [0.02, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:11:58,249", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.70.dcm 200 [0.02, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:11:58,286", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.71.dcm 200 [0.02, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:11:58,341", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.72.dcm 200 [0.04, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:11:58,403", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.73.dcm 200 [0.05, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:11:58,452", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.74.dcm 200 [0.04, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:11:58,504", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.75.dcm 200 [0.04, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:11:58,550", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.76.dcm 200 [0.03, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:11:58,598", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.77.dcm 200 [0.04, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:11:58,631", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.78.dcm 200 [0.02, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:11:58,661", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.79.dcm 200 [0.02, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:11:58,714", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.82.dcm 200 [0.01, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:11:58,783", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.86.dcm 200 [0.02, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:11:58,824", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.88.dcm 200 [0.01, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:11:58,852", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.89.dcm 200 [0.01, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:11:58,895", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.91.dcm 200 [0.02, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:11:58,922", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.92.dcm 200 [0.01, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:11:58,947", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.93.dcm 200 [0.01, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:11:58,988", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.95.dcm 200 [0.02, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:11:59,013", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.96.dcm 200 [0.01, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:11:59,055", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.98.dcm 200 [0.02, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:11:59,083", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.99.dcm 200 [0.01, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:11:59,114", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.100.dcm 200 [0.02, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:11:59,147", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.101.dcm 200 [0.02, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:11:59,178", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.102.dcm 200 [0.01, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:11:59,234", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.104.dcm 200 [0.03, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:11:59,268", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.105.dcm 200 [0.02, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:11:59,317", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.107.dcm 200 [0.02, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:11:59,348", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.108.dcm 200 [0.02, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:11:59,388", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.109.dcm 200 [0.03, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:11:59,414", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.110.dcm 200 [0.01, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:11:59,459", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.112.dcm 200 [0.02, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:11:59,513", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.114.dcm 200 [0.03, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:11:59,767", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.131.dcm 200 [0.03, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:11:59,822", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.132.dcm 200 [0.04, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:11:59,873", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.133.dcm 200 [0.04, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:11:59,906", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.134.dcm 200 [0.02, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:11:59,939", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.135.dcm 200 [0.02, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:11:59,973", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.136.dcm 200 [0.02, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:12:00,028", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.139.dcm 200 [0.02, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:12:00,097", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.143.dcm 200 [0.01, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:12:00,139", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.145.dcm 200 [0.02, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:12:00,222", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.150.dcm 200 [0.02, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:12:00,252", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.151.dcm 200 [0.02, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:12:00,482", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.167.dcm 200 [0.02, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:12:00,513", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.168.dcm 200 [0.02, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:12:00,542", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.169.dcm 200 [0.01, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:12:00,575", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.170.dcm 200 [0.02, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:12:00,606", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.171.dcm 200 [0.02, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:12:00,634", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.172.dcm 200 [0.02, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:12:00,663", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.173.dcm 200 [0.02, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:12:00,696", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.174.dcm 200 [0.02, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:12:00,739", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.175.dcm 200 [0.03, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:12:00,768", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.176.dcm 200 [0.01, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:12:00,804", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.177.dcm 200 [0.02, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:12:00,839", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.178.dcm 200 [0.02, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:12:00,886", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.179.dcm 200 [0.03, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:12:00,933", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.180.dcm 200 [0.02, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:12:00,962", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.181.dcm 200 [0.02, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:12:00,995", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.182.dcm 200 [0.02, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:12:01,032", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.183.dcm 200 [0.02, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:12:01,076", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.184.dcm 200 [0.03, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:12:01,119", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.185.dcm 200 [0.02, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:12:01,153", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.186.dcm 200 [0.02, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:12:01,190", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.187.dcm 200 [0.02, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:12:01,226", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.188.dcm 200 [0.02, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:12:01,262", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.189.dcm 200 [0.02, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:12:01,306", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.190.dcm 200 [0.03, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:12:01,342", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.191.dcm 200 [0.02, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:12:01,396", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.192.dcm 200 [0.03, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:12:01,424", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.193.dcm 200 [0.01, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:12:01,456", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.194.dcm 200 [0.02, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:12:01,484", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.195.dcm 200 [0.01, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:12:01,510", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.196.dcm 200 [0.01, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:12:01,538", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.197.dcm 200 [0.01, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:12:01,564", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.198.dcm 200 [0.01, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:12:01,591", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.199.dcm 200 [0.01, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:12:01,619", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.200.dcm 200 [0.01, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:12:01,648", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.201.dcm 200 [0.01, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:12:01,679", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.202.dcm 200 [0.02, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:12:01,707", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.203.dcm 200 [0.01, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:12:01,738", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.204.dcm 200 [0.02, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:12:01,765", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.205.dcm 200 [0.01, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:12:01,795", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.206.dcm 200 [0.02, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:12:01,824", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.207.dcm 200 [0.02, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:12:01,852", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.208.dcm 200 [0.01, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:12:01,878", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.209.dcm 200 [0.01, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:12:01,914", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.210.dcm 200 [0.02, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:12:01,941", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.211.dcm 200 [0.01, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:12:01,974", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.212.dcm 200 [0.02, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:12:02,015", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.213.dcm 200 [0.02, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:12:02,057", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.214.dcm 200 [0.02, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:12:02,097", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.215.dcm 200 [0.02, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:12:02,146", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.216.dcm 200 [0.04, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:12:02,196", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.217.dcm 200 [0.04, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:12:02,228", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.218.dcm 200 [0.02, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:12:02,274", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.219.dcm 200 [0.03, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:12:02,326", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.220.dcm 200 [0.03, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:12:02,373", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.221.dcm 200 [0.03, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:12:02,404", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.222.dcm 200 [0.02, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:12:02,439", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.223.dcm 200 [0.02, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:12:02,499", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.224.dcm 200 [0.04, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:12:02,550", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.225.dcm 200 [0.04, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:12:02,593", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.226.dcm 200 [0.03, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:12:02,631", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.227.dcm 200 [0.03, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:12:02,660", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.228.dcm 200 [0.01, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:12:02,701", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.229.dcm 200 [0.03, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:12:02,739", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.230.dcm 200 [0.02, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:12:02,792", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.231.dcm 200 [0.04, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:12:02,832", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.232.dcm 200 [0.03, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:12:02,875", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.233.dcm 200 [0.02, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:12:02,902", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.234.dcm 200 [0.01, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:12:02,933", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.235.dcm 200 [0.01, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:12:02,964", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.236.dcm 200 [0.01, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:12:02,991", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.237.dcm 200 [0.01, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:12:03,019", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.238.dcm 200 [0.01, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:12:03,049", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.239.dcm 200 [0.01, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:12:03,077", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.240.dcm 200 [0.01, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:12:03,111", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.241.dcm 200 [0.02, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:12:03,139", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.242.dcm 200 [0.01, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:12:03,166", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.243.dcm 200 [0.01, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:12:03,195", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.244.dcm 200 [0.01, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:12:03,233", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.245.dcm 200 [0.02, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:12:03,262", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.246.dcm 200 [0.01, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:12:03,290", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.247.dcm 200 [0.01, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:12:03,320", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.248.dcm 200 [0.01, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:12:03,350", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.249.dcm 200 [0.02, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:12:03,386", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.250.dcm 200 [0.02, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:12:03,413", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.251.dcm 200 [0.02, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:12:03,438", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.252.dcm 200 [0.01, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:12:03,471", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.253.dcm 200 [0.02, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:12:03,510", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.254.dcm 200 [0.03, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:12:03,561", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.255.dcm 200 [0.04, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:12:03,587", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.256.dcm 200 [0.01, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:12:03,619", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/3/1.2.840.113619.2.411.3.168459033.994.1618394979.117/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/1.2.840.113619.2.411.3.168459033.994.1618394979.197.257.dcm 200 [0.02, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:12:48,316", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/series/segmentation/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/ 200 [0.02, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:12:48,331", "level": "WARNING", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 176, "message": "HTTP GET /api/v1/series/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/params/ 404 [0.01, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:12:48,342", "level": "WARNING", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 176, "message": "HTTP GET /api/v1/series/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/slices/params/ 404 [0.01, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:12:58,164", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/series/segmentation/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/ 200 [0.03, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:12:58,185", "level": "WARNING", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 176, "message": "HTTP GET /api/v1/series/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/params/ 404 [0.02, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:12:58,200", "level": "WARNING", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 176, "message": "HTTP GET /api/v1/series/1.2.840.113619.2.411.3.168459033.994.1618394979.125.4/slices/params/ 404 [0.01, 127.0.0.1:50373]"}
{"time": "2025-05-30 15:20:40,033", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP POST /api/v1/exams/ 201 [0.67, 127.0.0.1:50754]"}
{"time": "2025-05-30 15:20:40,107", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/exams/ 200 [0.07, 127.0.0.1:50754]"}
{"time": "2025-05-30 15:20:42,564", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP OPTIONS /api/v1/series/segmentation/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/ 200 [0.00, 127.0.0.1:50754]"}
{"time": "2025-05-30 15:20:42,585", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 172, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.911257607240619696148712587012.dcm 304 [0.01, 127.0.0.1:50758]"}
{"time": "2025-05-30 15:20:42,593", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/series/segmentation/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/ 200 [0.03, 127.0.0.1:50754]"}
{"time": "2025-05-30 15:20:42,597", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP OPTIONS /api/v1/series/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/params/ 200 [0.00, 127.0.0.1:50754]"}
{"time": "2025-05-30 15:20:42,606", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/series/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/params/ 200 [0.01, 127.0.0.1:50754]"}
{"time": "2025-05-30 15:20:42,611", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP OPTIONS /api/v1/series/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/slices/params/ 200 [0.00, 127.0.0.1:50754]"}
{"time": "2025-05-30 15:20:42,628", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/series/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/slices/params/ 200 [0.02, 127.0.0.1:50754]"}
{"time": "2025-05-30 15:20:43,422", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 172, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.184801338381895107635381026857.dcm 304 [0.01, 127.0.0.1:50754]"}
{"time": "2025-05-30 15:20:43,424", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 172, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.227252232966919521559290115017.dcm 304 [0.01, 127.0.0.1:50758]"}
{"time": "2025-05-30 15:20:43,451", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 172, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.305763411933569638615761005863.dcm 304 [0.00, 127.0.0.1:50758]"}
{"time": "2025-05-30 15:20:43,465", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 172, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.892649213333065045357131733969.dcm 304 [0.01, 127.0.0.1:50758]"}
{"time": "2025-05-30 15:20:43,480", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 172, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.210616673074470338920284866510.dcm 304 [0.01, 127.0.0.1:50758]"}
{"time": "2025-05-30 15:20:43,494", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 172, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.308998466994580007520425874686.dcm 304 [0.01, 127.0.0.1:50758]"}
{"time": "2025-05-30 15:20:45,966", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 172, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.254916896710732470846876971852.dcm 304 [0.01, 127.0.0.1:50758]"}
{"time": "2025-05-30 15:20:45,983", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 172, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.255571364135736589219701492950.dcm 304 [0.00, 127.0.0.1:50758]"}
{"time": "2025-05-30 15:20:46,010", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 172, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.147995027728672345689485008579.dcm 304 [0.00, 127.0.0.1:50758]"}
{"time": "2025-05-30 15:20:46,039", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 172, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.204716343021109697619551710230.dcm 304 [0.00, 127.0.0.1:50758]"}
{"time": "2025-05-30 15:20:46,051", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 172, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.283914737330506508971730533367.dcm 304 [0.01, 127.0.0.1:50758]"}
{"time": "2025-05-30 15:20:46,086", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 172, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.184131899543495374569818432657.dcm 304 [0.01, 127.0.0.1:50758]"}
{"time": "2025-05-30 15:20:46,159", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 172, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.264364194296293440320787350583.dcm 304 [0.01, 127.0.0.1:50758]"}
{"time": "2025-05-30 15:20:46,509", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 172, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.216515627058008697217695696166.dcm 304 [0.02, 127.0.0.1:50758]"}
{"time": "2025-05-30 15:20:46,516", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 172, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.223280971440388131399634898721.dcm 304 [0.00, 127.0.0.1:50758]"}
{"time": "2025-05-30 15:20:46,575", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 172, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.218387194706247505045205169130.dcm 304 [0.01, 127.0.0.1:50758]"}
{"time": "2025-05-30 15:20:46,623", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 172, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.241073611031000247506142606387.dcm 304 [0.00, 127.0.0.1:50758]"}
{"time": "2025-05-30 15:20:48,846", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 172, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.136943255924913899762603730997.dcm 304 [0.01, 127.0.0.1:50758]"}
{"time": "2025-05-30 15:20:48,863", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 172, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.301905642463494729146015070819.dcm 304 [0.00, 127.0.0.1:50758]"}
{"time": "2025-05-30 15:23:45,352", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/series/segmentation/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/ 200 [0.01, 127.0.0.1:50814]"}
{"time": "2025-05-30 15:23:45,363", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/series/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/params/ 200 [0.01, 127.0.0.1:50814]"}
{"time": "2025-05-30 15:23:45,377", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/series/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/slices/params/ 200 [0.01, 127.0.0.1:50814]"}
{"time": "2025-05-30 15:23:48,503", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/series/segmentation/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/ 200 [0.03, 127.0.0.1:50814]"}
{"time": "2025-05-30 15:23:48,523", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/series/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/params/ 200 [0.01, 127.0.0.1:50814]"}
{"time": "2025-05-30 15:23:48,543", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/series/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/slices/params/ 200 [0.02, 127.0.0.1:50814]"}
{"time": "2025-05-30 15:59:44,586", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/exams/ 200 [0.06, 127.0.0.1:51115]"}
{"time": "2025-05-30 16:01:07,880", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP POST /api/v1/exams/ 201 [0.57, 127.0.0.1:51119]"}
{"time": "2025-05-30 16:01:07,945", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/exams/ 200 [0.06, 127.0.0.1:51119]"}
{"time": "2025-05-30 16:01:08,919", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/series/segmentation/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/ 200 [0.01, 127.0.0.1:51119]"}
{"time": "2025-05-30 16:01:08,942", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/series/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/params/ 200 [0.01, 127.0.0.1:51119]"}
{"time": "2025-05-30 16:01:08,953", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/series/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/slices/params/ 200 [0.01, 127.0.0.1:51119]"}
{"time": "2025-06-02 14:26:36,455", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 172, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.136943255924913899762603730997.dcm 304 [0.00, 127.0.0.1:65184]"}
{"time": "2025-06-02 14:26:36,464", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 172, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.301905642463494729146015070819.dcm 304 [0.00, 127.0.0.1:65184]"}
{"time": "2025-06-03 10:24:03,417", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP OPTIONS /api/v1/series/segmentation/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/ 200 [0.00, 127.0.0.1:62918]"}
{"time": "2025-06-03 10:24:03,424", "level": "WARNING", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 178, "message": "HTTP GET /api/v1/series/segmentation/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/ 401 [0.01, 127.0.0.1:62918]"}
{"time": "2025-06-06 09:30:27,076", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP OPTIONS /api/v1/exams/ 200 [0.00, 127.0.0.1:64112]"}
{"time": "2025-06-06 09:30:27,077", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP OPTIONS /api/v1/exams/ 200 [0.00, 127.0.0.1:64113]"}
{"time": "2025-06-06 09:30:27,084", "level": "WARNING", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 178, "message": "HTTP GET /api/v1/exams/ 401 [0.01, 127.0.0.1:64112]"}
{"time": "2025-06-06 09:30:27,092", "level": "WARNING", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 178, "message": "HTTP GET /api/v1/exams/ 401 [0.01, 127.0.0.1:64112]"}
{"time": "2025-06-06 09:30:36,382", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP OPTIONS /api/v1/authentication/token/ 200 [0.00, 127.0.0.1:64112]"}
{"time": "2025-06-06 09:30:37,434", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP POST /api/v1/authentication/token/ 200 [1.05, 127.0.0.1:64112]"}
{"time": "2025-06-06 09:30:37,525", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/exams/ 200 [0.06, 127.0.0.1:64112]"}
{"time": "2025-06-06 09:30:37,579", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/exams/ 200 [0.05, 127.0.0.1:64113]"}
{"time": "2025-06-06 09:30:39,669", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP OPTIONS /api/v1/series/segmentation/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/ 200 [0.00, 127.0.0.1:64113]"}
{"time": "2025-06-06 09:30:39,690", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.911257607240619696148712587012.dcm 200 [0.02, 127.0.0.1:64112]"}
{"time": "2025-06-06 09:30:39,695", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/series/segmentation/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/ 200 [0.02, 127.0.0.1:64113]"}
{"time": "2025-06-06 09:30:39,713", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP OPTIONS /api/v1/series/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/params/ 200 [0.00, 127.0.0.1:64113]"}
{"time": "2025-06-06 09:30:39,727", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/series/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/params/ 200 [0.01, 127.0.0.1:64113]"}
{"time": "2025-06-06 09:30:39,731", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP OPTIONS /api/v1/series/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/slices/params/ 200 [0.00, 127.0.0.1:64113]"}
{"time": "2025-06-06 09:30:39,745", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/series/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/slices/params/ 200 [0.01, 127.0.0.1:64113]"}
{"time": "2025-06-06 09:38:26,526", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP OPTIONS /api/v1/password-reset/ 200 [0.00, 127.0.0.1:64181]"}
{"time": "2025-06-06 09:38:26,537", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP POST /api/v1/password-reset/ 200 [0.01, 127.0.0.1:64181]"}
{"time": "2025-07-05 16:02:30,860", "level": "INFO", "logger": "django.utils.autoreload", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\django\utils\autoreload.py", "line": 667, "message": "Watching for file changes with StatReloader"}
{"time": "2025-07-05 16:02:43,305", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP OPTIONS /api/v1/authentication/token/ 200 [0.00, 127.0.0.1:56807]"}
{"time": "2025-07-05 16:02:44,345", "level": "WARNING", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 178, "message": "HTTP POST /api/v1/authentication/token/ 401 [1.04, 127.0.0.1:56807]"}
{"time": "2025-07-05 16:02:49,995", "level": "WARNING", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 178, "message": "HTTP POST /api/v1/authentication/token/ 401 [1.01, 127.0.0.1:56807]"}
{"time": "2025-07-05 16:02:55,958", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP POST /api/v1/authentication/token/ 200 [1.14, 127.0.0.1:56807]"}
{"time": "2025-07-05 16:02:55,998", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP OPTIONS /api/v1/exams/ 200 [0.00, 127.0.0.1:56807]"}
{"time": "2025-07-05 16:02:56,072", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/exams/ 200 [0.07, 127.0.0.1:56807]"}
{"time": "2025-07-05 16:02:56,131", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/exams/ 200 [0.06, 127.0.0.1:56816]"}
{"time": "2025-07-05 16:02:58,795", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP OPTIONS /api/v1/series/segmentation/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/ 200 [0.00, 127.0.0.1:56816]"}
{"time": "2025-07-05 16:02:58,827", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/series/segmentation/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/ 200 [0.03, 127.0.0.1:56816]"}
{"time": "2025-07-05 16:02:58,832", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP OPTIONS /api/v1/series/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/params/ 200 [0.00, 127.0.0.1:56816]"}
{"time": "2025-07-05 16:02:58,852", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/series/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/params/ 200 [0.02, 127.0.0.1:56816]"}
{"time": "2025-07-05 16:02:58,856", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP OPTIONS /api/v1/series/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/slices/params/ 200 [0.00, 127.0.0.1:56816]"}
{"time": "2025-07-05 16:02:58,873", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.911257607240619696148712587012.dcm 200 [0.07, 127.0.0.1:56807]"}
{"time": "2025-07-05 16:02:58,882", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/series/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/slices/params/ 200 [0.02, 127.0.0.1:56816]"}
{"time": "2025-07-05 16:02:59,811", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.184801338381895107635381026857.dcm 200 [0.01, 127.0.0.1:56816]"}
{"time": "2025-07-05 16:02:59,829", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.227252232966919521559290115017.dcm 200 [0.00, 127.0.0.1:56816]"}
{"time": "2025-07-05 16:02:59,855", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.305763411933569638615761005863.dcm 200 [0.00, 127.0.0.1:56816]"}
{"time": "2025-07-05 16:02:59,875", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.892649213333065045357131733969.dcm 200 [0.01, 127.0.0.1:56816]"}
{"time": "2025-07-05 16:02:59,901", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.210616673074470338920284866510.dcm 200 [0.01, 127.0.0.1:56816]"}
{"time": "2025-07-05 16:03:00,245", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.308998466994580007520425874686.dcm 200 [0.02, 127.0.0.1:56816]"}
{"time": "2025-07-05 16:03:00,249", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.254916896710732470846876971852.dcm 200 [0.01, 127.0.0.1:56807]"}
{"time": "2025-07-05 16:03:00,270", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.255571364135736589219701492950.dcm 200 [0.00, 127.0.0.1:56807]"}
{"time": "2025-07-05 16:03:00,298", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.147995027728672345689485008579.dcm 200 [0.01, 127.0.0.1:56807]"}
{"time": "2025-07-05 16:03:00,348", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.204716343021109697619551710230.dcm 200 [0.02, 127.0.0.1:56807]"}
{"time": "2025-07-05 16:03:00,763", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.283914737330506508971730533367.dcm 200 [0.00, 127.0.0.1:56807]"}
{"time": "2025-07-05 16:03:00,777", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.184131899543495374569818432657.dcm 200 [0.00, 127.0.0.1:56807]"}
{"time": "2025-07-05 16:03:00,791", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.264364194296293440320787350583.dcm 200 [0.01, 127.0.0.1:56807]"}
{"time": "2025-07-05 16:03:00,818", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.216515627058008697217695696166.dcm 200 [0.01, 127.0.0.1:56807]"}
{"time": "2025-07-05 16:03:00,844", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /media/exams/2/1.3.6.1.4.1.14519.5.2.1.6655.2359.165554066086145834377508507990/1.3.6.1.4.1.14519.5.2.1.6655.2359.257508444832901632590301540805/1.3.6.1.4.1.14519.5.2.1.6655.2359.223280971440388131399634898721.dcm 200 [0.01, 127.0.0.1:56807]"}
{"time": "2025-07-05 16:03:05,296", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/exams/ 200 [0.06, 127.0.0.1:56807]"}
{"time": "2025-07-05 16:03:05,351", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/exams/ 200 [0.05, 127.0.0.1:56816]"}
{"time": "2025-07-05 16:04:30,858", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP POST /api/v1/authentication/token/ 200 [0.99, 127.0.0.1:56885]"}
{"time": "2025-07-05 16:04:30,944", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/exams/ 200 [0.06, 127.0.0.1:56885]"}
{"time": "2025-07-05 16:04:31,010", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /api/v1/exams/ 200 [0.06, 127.0.0.1:56887]"}
{"time": "2025-07-05 16:04:35,869", "level": "WARNING", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 178, "message": "HTTP POST /api/v1/authentication/token/ 401 [0.99, 127.0.0.1:56887]"}
{"time": "2025-07-05 16:05:02,776", "level": "WARNING", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 178, "message": "HTTP GET /api/v1/authentication/token/ 405 [0.22, 127.0.0.1:56906]"}
{"time": "2025-07-05 16:05:02,815", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /static/rest_framework/css/bootstrap.min.css 200 [0.02, 127.0.0.1:56906]"}
{"time": "2025-07-05 16:05:02,824", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /static/rest_framework/css/bootstrap-tweaks.css 200 [0.02, 127.0.0.1:56907]"}
{"time": "2025-07-05 16:05:02,825", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /static/rest_framework/css/prettify.css 200 [0.02, 127.0.0.1:56909]"}
{"time": "2025-07-05 16:05:02,827", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /static/rest_framework/css/default.css 200 [0.02, 127.0.0.1:56910]"}
{"time": "2025-07-05 16:05:02,833", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /static/rest_framework/js/ajax-form.js 200 [0.03, 127.0.0.1:56912]"}
{"time": "2025-07-05 16:05:02,838", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /static/rest_framework/js/csrf.js 200 [0.02, 127.0.0.1:56906]"}
{"time": "2025-07-05 16:05:02,848", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /static/rest_framework/js/default.js 200 [0.02, 127.0.0.1:56910]"}
{"time": "2025-07-05 16:05:02,848", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /static/rest_framework/js/load-ajax-form.js 200 [0.01, 127.0.0.1:56912]"}
{"time": "2025-07-05 16:05:02,853", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /static/rest_framework/js/prettify-min.js 200 [0.02, 127.0.0.1:56909]"}
{"time": "2025-07-05 16:05:02,860", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /static/rest_framework/js/bootstrap.min.js 200 [0.03, 127.0.0.1:56907]"}
{"time": "2025-07-05 16:05:02,869", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /static/rest_framework/js/jquery-3.7.1.min.js 200 [0.06, 127.0.0.1:56911]"}
{"time": "2025-07-05 16:05:02,980", "level": "INFO", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 168, "message": "HTTP GET /static/rest_framework/img/grid.png 200 [0.01, 127.0.0.1:56911]"}
{"time": "2025-07-05 16:05:03,052", "level": "WARNING", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 176, "message": "HTTP GET /favicon.ico 404 [0.02, 127.0.0.1:56911]"}
{"time": "2025-07-05 16:05:21,857", "level": "WARNING", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 178, "message": "HTTP POST /api/v1/authentication/token/ 401 [1.05, 127.0.0.1:56887]"}
{"time": "2025-07-05 16:05:37,984", "level": "WARNING", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 178, "message": "HTTP POST /api/v1/authentication/token/ 401 [0.99, 127.0.0.1:56887]"}
{"time": "2025-07-05 16:08:51,441", "level": "INFO", "logger": "django.utils.autoreload", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\django\utils\autoreload.py", "line": 265, "message": "C:\Users\<USER>\Desktop\Lisa\SIATCT\siatct\authentication\serializers.py changed, reloading."}
{"time": "2025-07-05 16:08:53,383", "level": "INFO", "logger": "django.utils.autoreload", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\django\utils\autoreload.py", "line": 667, "message": "Watching for file changes with StatReloader"}
{"time": "2025-07-05 16:09:05,563", "level": "INFO", "logger": "django.utils.autoreload", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\django\utils\autoreload.py", "line": 265, "message": "C:\Users\<USER>\Desktop\Lisa\SIATCT\siatct\authentication\serializers.py changed, reloading."}
{"time": "2025-07-05 16:09:07,375", "level": "INFO", "logger": "django.utils.autoreload", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\django\utils\autoreload.py", "line": 667, "message": "Watching for file changes with StatReloader"}
{"time": "2025-07-05 16:09:19,578", "level": "INFO", "logger": "django.utils.autoreload", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\django\utils\autoreload.py", "line": 265, "message": "C:\Users\<USER>\Desktop\Lisa\SIATCT\siatct\authentication\views.py changed, reloading."}
{"time": "2025-07-05 16:09:21,272", "level": "INFO", "logger": "django.utils.autoreload", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\django\utils\autoreload.py", "line": 667, "message": "Watching for file changes with StatReloader"}
{"time": "2025-07-05 16:09:34,005", "level": "INFO", "logger": "django.utils.autoreload", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\django\utils\autoreload.py", "line": 265, "message": "C:\Users\<USER>\Desktop\Lisa\SIATCT\siatct\authentication\views.py changed, reloading."}
{"time": "2025-07-05 16:09:35,691", "level": "INFO", "logger": "django.utils.autoreload", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\django\utils\autoreload.py", "line": 667, "message": "Watching for file changes with StatReloader"}
{"time": "2025-07-05 16:09:54,050", "level": "INFO", "logger": "django.utils.autoreload", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\django\utils\autoreload.py", "line": 265, "message": "C:\Users\<USER>\Desktop\Lisa\SIATCT\siatct\authentication\urls.py changed, reloading."}
{"time": "2025-07-05 16:09:55,806", "level": "INFO", "logger": "django.utils.autoreload", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\django\utils\autoreload.py", "line": 667, "message": "Watching for file changes with StatReloader"}
{"time": "2025-07-05 16:10:13,500", "level": "INFO", "logger": "django.utils.autoreload", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\django\utils\autoreload.py", "line": 265, "message": "C:\Users\<USER>\Desktop\Lisa\SIATCT\siatct\authentication\urls.py changed, reloading."}
{"time": "2025-07-05 16:10:14,924", "level": "INFO", "logger": "django.utils.autoreload", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\django\utils\autoreload.py", "line": 667, "message": "Watching for file changes with StatReloader"}
{"time": "2025-07-05 16:13:56,474", "level": "INFO", "logger": "django.utils.autoreload", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\django\utils\autoreload.py", "line": 265, "message": "C:\Users\<USER>\Desktop\Lisa\SIATCT\siatct\authentication\views.py changed, reloading."}
{"time": "2025-07-05 16:13:58,258", "level": "INFO", "logger": "django.utils.autoreload", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\django\utils\autoreload.py", "line": 667, "message": "Watching for file changes with StatReloader"}
{"time": "2025-07-05 16:14:11,296", "level": "INFO", "logger": "django.utils.autoreload", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\django\utils\autoreload.py", "line": 265, "message": "C:\Users\<USER>\Desktop\Lisa\SIATCT\siatct\authentication\views.py changed, reloading."}
{"time": "2025-07-05 16:14:13,010", "level": "INFO", "logger": "django.utils.autoreload", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\django\utils\autoreload.py", "line": 667, "message": "Watching for file changes with StatReloader"}
{"time": "2025-07-05 16:14:23,336", "level": "INFO", "logger": "django.utils.autoreload", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\django\utils\autoreload.py", "line": 265, "message": "C:\Users\<USER>\Desktop\Lisa\SIATCT\siatct\authentication\views.py changed, reloading."}
{"time": "2025-07-05 16:14:25,015", "level": "INFO", "logger": "django.utils.autoreload", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\django\utils\autoreload.py", "line": 667, "message": "Watching for file changes with StatReloader"}
{"time": "2025-07-05 16:17:05,996", "level": "WARNING", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 178, "message": "HTTP POST /api/v1/authentication/token/ 401 [0.98, 127.0.0.1:57783]"}
{"time": "2025-07-05 16:17:09,788", "level": "WARNING", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 178, "message": "HTTP POST /api/v1/authentication/token/ 401 [1.05, 127.0.0.1:57783]"}
{"time": "2025-07-05 16:17:22,265", "level": "WARNING", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 178, "message": "HTTP POST /api/v1/authentication/token/ 401 [1.09, 127.0.0.1:57783]"}
{"time": "2025-07-05 16:19:23,974", "level": "INFO", "logger": "django.utils.autoreload", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\django\utils\autoreload.py", "line": 265, "message": "C:\Users\<USER>\Desktop\Lisa\SIATCT\siatct\authentication\views.py changed, reloading."}
{"time": "2025-07-05 16:19:25,729", "level": "INFO", "logger": "django.utils.autoreload", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\django\utils\autoreload.py", "line": 667, "message": "Watching for file changes with StatReloader"}
{"time": "2025-07-05 16:19:36,291", "level": "INFO", "logger": "django.utils.autoreload", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\django\utils\autoreload.py", "line": 265, "message": "C:\Users\<USER>\Desktop\Lisa\SIATCT\siatct\authentication\views.py changed, reloading."}
{"time": "2025-07-05 16:19:37,734", "level": "INFO", "logger": "django.utils.autoreload", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\django\utils\autoreload.py", "line": 667, "message": "Watching for file changes with StatReloader"}
{"time": "2025-07-05 16:24:27,319", "level": "WARNING", "logger": "django.channels.server", "path": "C:\Users\<USER>\Desktop\Lisa\SIATCT\.venv\Lib\site-packages\daphne\management\commands\runserver.py", "line": 178, "message": "HTTP POST /api/v1/authentication/token/ 401 [1.04, 127.0.0.1:58154]"}
