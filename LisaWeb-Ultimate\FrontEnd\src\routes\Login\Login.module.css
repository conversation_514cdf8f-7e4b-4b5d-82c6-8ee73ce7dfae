.login {
    display: flex;
    justify-content: center;
    align-items: center;
    flex: 1; 
    background: #EBE5E0;
}

.loginForms{
    background-color: white;
    width:450px;
    min-width: 400px;
    border: 2px solid #13D8A3;
    border-radius: 20px;
}

#buttonCtm{
    background-color: #13D8A3;
    color:white;
    width: 100%;
}

.checkLabel{
    display: flex;
    justify-content: space-between;
    font-size:14px;
}

.checkLabel span{
    cursor: pointer;
}

.checkLabel a{
    text-decoration: underline !important;
    text-underline-offset: 3px;
}

#formGroupCtm:focus {
    outline: none;
    border: 0px;
    box-shadow: 0 0 0 0.2rem #13D8A3;
}
#formGroupCtmPas:focus{
    outline: none;
    border: 0px;
    box-shadow: 0 0 0 0.2rem #13D8A3;
}
#formGroupCtmChe:focus{
    outline: none;
    border: 0px;
    box-shadow: 0 0 0 0.2rem #13D8A3;
}

#formGroupCtmChe:checked {
    border:0;
    background-color: #13D8A3;;
    box-shadow: 0 0 0 0.2rem #13D8A3;
}

/* Error state styles */
.loginForms .form-control.is-invalid {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.loginForms .form-control.is-invalid:focus {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

/* Loading state styles */
.loginForms .form-control:disabled {
    background-color: #f8f9fa;
    opacity: 0.65;
}

#buttonCtm:disabled {
    background-color: #6c757d;
    border-color: #6c757d;
    opacity: 0.65;
}

/* Alert styles */
.loginForms .alert {
    border-radius: 8px;
    font-size: 14px;
}

@media (max-width:600px) {
    .loginForms{
        min-width: 300px;
        margin: 20px;
    }
}