//css and bootstrap
import styles from './Login.module.css'
import Button from 'react-bootstrap/Button';
import Form from 'react-bootstrap/Form';
import Alert from 'react-bootstrap/Alert';
import Spinner from 'react-bootstrap/Spinner';
//react router
import { useNavigate, Link } from 'react-router-dom';
//react
import { useState, useContext, useRef, useEffect } from 'react';
//axios
import axios from '../../api/axios';
//context
import { AuthContext } from '../../context/AuthProvider';

function Login() {
    const userRef = useRef();
    const [user, setUser] = useState('');
    const [password, setPassword] = useState('');
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState('');
    const [validationErrors, setValidationErrors] = useState({});
    const [retryCount, setRetryCount] = useState(0);
    const { authData, setAuthData } = useContext(AuthContext);

    const tokenUrl = '/api/v1/authentication/token/';
    const navigate = useNavigate();

    useEffect(() => {
        userRef.current.focus();

        // Verifica se já existe um token no localStorage
        try {
            const storedAuthData = JSON.parse(localStorage.getItem('authData'));
            if (storedAuthData && storedAuthData.access) {
                setAuthData(storedAuthData);
                navigate('/home');
            }
        } catch (error) {
            // Clear invalid stored data
            localStorage.removeItem('authData');
        }
    }, []);

    // Handle keyboard shortcuts
    useEffect(() => {
        const handleKeyPress = (e) => {
            // Allow Enter to submit form when not in a button
            if (e.key === 'Enter' && e.target.tagName !== 'BUTTON' && !loading) {
                e.preventDefault();
                handleSubmit(e);
            }
        };

        document.addEventListener('keypress', handleKeyPress);
        return () => document.removeEventListener('keypress', handleKeyPress);
    }, [loading, user, password]);

    // Clear errors when user starts typing
    useEffect(() => {
        setError('');
        setValidationErrors({});
        setRetryCount(0);
    }, [user, password]);

    const validateForm = () => {
        const errors = {};

        if (!user.trim()) {
            errors.username = 'Nome de usuário é obrigatório';
        }

        if (!password.trim()) {
            errors.password = 'Senha é obrigatória';
        }

        setValidationErrors(errors);
        return Object.keys(errors).length === 0;
    };

    const getErrorMessage = (error) => {
        // Handle network errors
        if (!error.response) {
            if (error.code === 'NETWORK_ERROR' || error.message === 'Network Error') {
                return 'Erro de conexão. Verifique sua internet e tente novamente.';
            }
            if (error.code === 'ECONNREFUSED') {
                return 'Servidor indisponível. Tente novamente em alguns instantes.';
            }
            if (error.code === 'TIMEOUT') {
                return 'Tempo limite excedido. Verifique sua conexão e tente novamente.';
            }
            return 'Erro de conexão com o servidor. Tente novamente em alguns instantes.';
        }

        const status = error.response.status;
        const data = error.response.data;

        // Handle specific HTTP status codes
        switch (status) {
            case 400:
                // Bad Request - usually validation errors or wrong credentials
                if (data?.detail) {
                    // Standard JWT error messages
                    const detail = data.detail.toLowerCase();
                    if (detail.includes('credentials') ||
                        detail.includes('authentication') ||
                        detail.includes('invalid') ||
                        detail.includes('incorrect')) {
                        return 'Usuário ou senha incorretos. Verifique suas credenciais e tente novamente.';
                    }
                    if (detail.includes('required')) {
                        return 'Todos os campos são obrigatórios.';
                    }
                    if (detail.includes('blank') || detail.includes('empty')) {
                        return 'Usuário e senha não podem estar vazios.';
                    }
                    return data.detail;
                }
                if (data?.error) {
                    const error = data.error.toLowerCase();
                    if (error.includes('usuário') && error.includes('senha')) {
                        return data.error;
                    }
                    if (error.includes('obrigatório')) {
                        return data.error;
                    }
                    return data.error;
                }
                if (data?.non_field_errors) {
                    return data.non_field_errors[0];
                }
                // Handle field-specific errors
                if (data?.username) {
                    return `Usuário: ${data.username[0]}`;
                }
                if (data?.password) {
                    return `Senha: ${data.password[0]}`;
                }
                return 'Dados inválidos. Verifique suas informações e tente novamente.';

            case 401:
                // Unauthorized - invalid credentials
                if (data?.detail) {
                    const detail = data.detail.toLowerCase();
                    if (detail.includes('token') || detail.includes('authentication')) {
                        return 'Sessão expirada. Faça login novamente.';
                    }
                }
                return 'Usuário ou senha incorretos. Verifique suas credenciais e tente novamente.';

            case 403:
                // Forbidden - account might be disabled
                if (data?.detail) {
                    const detail = data.detail.toLowerCase();
                    if (detail.includes('disabled') || detail.includes('inactive')) {
                        return 'Sua conta está desativada. Entre em contato com o suporte.';
                    }
                    if (detail.includes('permission') || detail.includes('access')) {
                        return 'Acesso negado. Entre em contato com o suporte.';
                    }
                }
                return 'Acesso negado. Sua conta pode estar desativada. Entre em contato com o suporte.';

            case 404:
                // Not Found - endpoint doesn't exist
                return 'Serviço de autenticação não encontrado. Entre em contato com o suporte.';

            case 429:
                // Too Many Requests - rate limiting
                return 'Muitas tentativas de login. Aguarde alguns minutos antes de tentar novamente.';

            case 500:
                // Internal Server Error
                return 'Erro interno do servidor. Tente novamente em alguns instantes.';

            case 502:
                // Bad Gateway
                return 'Servidor temporariamente indisponível. Tente novamente em alguns instantes.';

            case 503:
                // Service Unavailable
                return 'Serviço temporariamente indisponível. Tente novamente em alguns instantes.';

            case 504:
                // Gateway Timeout
                return 'Tempo limite do servidor excedido. Tente novamente em alguns instantes.';

            default:
                // Generic error message for other status codes
                if (data?.error) {
                    return data.error;
                }
                if (data?.detail) {
                    return data.detail;
                }
                return 'Erro inesperado. Tente novamente.';
        }
    };

    const attemptLogin = async (userData, attempt = 1) => {
        try {
            const response = await axios.post(tokenUrl, userData);
            setAuthData(response.data);
            localStorage.setItem('authData', JSON.stringify(response.data));
            navigate("/home");
            return true;
        } catch (err) {
            // Check if it's a network error and we haven't exceeded retry limit
            if (!err.response && attempt < 3) {
                // Wait before retrying (exponential backoff)
                await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
                return attemptLogin(userData, attempt + 1);
            }
            throw err;
        }
    };

    const handleSubmit = async (e) => {
        e.preventDefault();

        // Clear previous errors
        setError('');
        setValidationErrors({});

        // Validate form
        if (!validateForm()) {
            return;
        }

        setLoading(true);

        const userData = {
            'username': user.trim(),
            'password': password
        };

        try {
            await attemptLogin(userData);
        } catch (err) {
            console.error('Login error:', err);
            const errorMessage = getErrorMessage(err);
            setError(errorMessage);
            setRetryCount(prev => prev + 1);
        } finally {
            setLoading(false);
        }
    };

    const handleRetry = () => {
        setError('');
        setValidationErrors({});
        handleSubmit({ preventDefault: () => {} });
    };

    return (
        <section className={styles.login}>
            <Form className={`${styles.loginForms} p-5 rounded`} onSubmit={handleSubmit}>
                <h2 className="text-center mb-4">Entrar</h2>

                {/* Global error message */}
                {error && (
                    <Alert variant="danger" className="mb-3">
                        <div>{error}</div>
                        {(error.includes('conexão') || error.includes('servidor') || error.includes('rede')) && (
                            <Button
                                variant="outline-danger"
                                size="sm"
                                className="mt-2"
                                onClick={handleRetry}
                                disabled={loading}
                            >
                                Tentar novamente
                            </Button>
                        )}
                    </Alert>
                )}

                <Form.Group className="mb-3" controlId="username">
                    <Form.Label>Nome de usuário</Form.Label>
                    <Form.Control
                        ref={userRef}
                        type="text"
                        name="username"
                        placeholder="Digite seu usuário"
                        onChange={(e) => setUser(e.target.value)}
                        value={user}
                        isInvalid={!!validationErrors.username}
                        disabled={loading}
                        autoComplete="username"
                        aria-describedby={validationErrors.username ? "username-error" : undefined}
                        required
                    />
                    <Form.Control.Feedback type="invalid" id="username-error">
                        {validationErrors.username}
                    </Form.Control.Feedback>
                </Form.Group>

                <Form.Group className="mb-3" controlId="password">
                    <Form.Label>Senha</Form.Label>
                    <Form.Control
                        type="password"
                        name="password"
                        placeholder="Digite sua senha"
                        onChange={(e) => setPassword(e.target.value)}
                        value={password}
                        isInvalid={!!validationErrors.password}
                        disabled={loading}
                        autoComplete="current-password"
                        aria-describedby={validationErrors.password ? "password-error" : undefined}
                        required
                    />
                    <Form.Control.Feedback type="invalid" id="password-error">
                        {validationErrors.password}
                    </Form.Control.Feedback>
                </Form.Group>

                <Form.Group className={`${styles.checkLabel} mb-3`} controlId={styles.formGroupCtmChe}>
                    <span><Form.Check type="checkbox" label="Matenha-me logado" disabled={loading} /></span>
                    <span><Link to='/forgot-password'>Esqueceu a senha?</Link></span>
                </Form.Group>

                <Button
                    variant=""
                    type="submit"
                    id={styles.buttonCtm}
                    disabled={loading}
                    className="d-flex align-items-center justify-content-center"
                >
                    {loading ? (
                        <>
                            <Spinner
                                as="span"
                                animation="border"
                                size="sm"
                                role="status"
                                aria-hidden="true"
                                className="me-2"
                            />
                            Entrando...
                        </>
                    ) : (
                        'Entrar'
                    )}
                </Button>
            </Form>
        </section>
    );
}

export default Login    